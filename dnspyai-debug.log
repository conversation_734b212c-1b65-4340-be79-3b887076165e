[2025-06-18 15:29:50.835] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:29:50.836] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:29:50.836] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:29:50.836] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:29:50.836] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:29:55.826] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:29:55.827] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:29:55.827] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:29:55.827] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:29:55.828] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:29:55.828] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:29:55.828] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:29:55.828] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:29:55.828] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:29:55.828] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:29:55.829] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:29:55.829] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:29:55.834] Extension: Window content type: Grid
[2025-06-18 15:29:55.834] Extension: AI Panel added to Grid
[2025-06-18 15:29:55.834] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:30:00.841] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:00.841] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:00.842] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:00.842] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:00.842] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:00.842] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:00.843] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:05.847] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:05.848] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:05.848] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:05.849] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:05.849] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:05.849] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:05.849] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:10.842] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:10.842] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:10.843] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:10.843] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:10.843] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:10.844] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:10.844] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:12.055] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:30:12.065] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:30:15.848] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:15.848] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:15.848] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:15.848] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:15.848] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:15.849] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:15.849] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:20.863] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:20.863] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:20.863] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:20.863] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:20.863] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:20.864] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:20.864] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:21.976] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:30:22.000] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:30:25.842] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:25.842] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:25.842] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:25.842] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:25.842] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:25.842] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:25.842] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:30.851] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:30.851] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:30.852] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:30.852] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:30.852] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:30.852] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:30.853] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:35.846] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:35.846] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:35.847] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:35.847] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:35.847] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:35.847] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:35.847] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:40.842] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:40.842] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:40.843] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:40.843] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:40.843] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:40.843] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:40.844] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:45.848] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:45.848] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:45.848] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:45.849] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:45.849] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:45.850] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:45.850] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:50.864] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:50.865] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:50.865] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:50.865] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:50.865] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:50.866] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:50.866] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:30:55.858] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:30:55.858] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:30:55.859] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:30:55.859] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:30:55.860] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:30:55.860] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:30:55.860] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:31:00.861] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:31:00.861] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:31:00.861] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:31:00.862] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:31:00.862] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:31:00.862] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:31:00.863] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:31:05.861] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:31:05.862] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:31:05.862] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:31:05.863] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:31:05.863] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:31:05.863] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:31:05.864] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:31:10.855] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:31:10.856] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:31:10.856] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:31:10.856] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:31:10.856] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:31:10.857] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:31:10.857] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:31:15.863] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:31:15.863] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:31:15.864] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:31:15.864] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:31:15.864] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:31:15.864] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:31:15.865] Extension: SKIP - Window MethodBodyDlg already has AI Panel
