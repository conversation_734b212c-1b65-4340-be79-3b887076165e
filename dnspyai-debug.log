[2025-06-18 15:08:32.508] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:08:32.512] Extension: Initializing AI services...
[2025-06-18 15:08:32.541] Extension: AI Service initialized: True
[2025-06-18 15:08:32.541] Extension: Initialization completed successfully
[2025-06-18 15:08:32.785] Extension: Window monitoring started
[2025-06-18 15:08:33.121] === DnSpyAI Extension Started ===
[2025-06-18 15:08:33.121] Current Time: 18.06.2025 15:08:33
[2025-06-18 15:08:33.122] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:08:33.122] DebugHelper constructor completed successfully
[2025-06-18 15:08:33.122] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:08:33.122] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:08:37.792] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:08:37.792] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:08:37.793] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:08:37.793] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:08:37.793] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:08:37.794] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:08:37.794] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:08:37.794] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:08:37.794] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:08:37.794] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:08:37.795] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:08:37.795] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:08:37.802] Extension: Window content type: Grid
[2025-06-18 15:08:37.803] Extension: AI Panel added to Grid
[2025-06-18 15:08:37.804] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:08:42.780] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:08:42.781] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:08:42.781] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:08:42.781] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:08:42.781] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:08:42.782] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:08:42.782] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:08:47.791] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:08:47.791] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:08:47.791] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:08:47.791] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:08:47.791] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:08:47.792] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:08:47.792] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:08:52.798] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:08:52.799] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:08:52.799] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:08:52.800] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:08:52.800] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:08:52.800] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:08:52.800] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:08:57.022] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:08:57.032] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:08:57.797] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:08:57.798] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:08:57.798] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:08:57.798] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:08:57.798] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:08:57.798] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:08:57.798] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:02.797] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:02.797] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:02.797] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:02.797] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:02.797] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:02.797] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:02.797] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:05.634] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:09:05.654] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:09:07.824] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:07.825] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:07.825] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:07.825] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:07.825] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:07.825] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:07.826] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:12.825] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:12.826] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:12.826] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:12.826] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:12.826] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:12.826] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:12.826] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:17.830] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:17.831] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:17.831] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:17.831] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:17.831] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:17.832] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:17.832] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:22.833] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:22.834] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:22.834] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:22.834] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:22.834] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:22.834] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:22.835] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:27.845] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:27.845] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:27.846] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:27.846] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:27.846] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:27.846] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:27.847] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:32.841] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:32.841] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:32.842] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:32.842] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:32.842] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:32.842] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:32.842] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:37.844] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:37.845] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:37.845] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:37.845] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:37.845] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:37.846] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:37.846] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:42.845] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:42.845] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:42.845] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:42.846] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:42.846] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:42.846] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:42.846] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:47.846] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:47.846] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:47.846] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:47.847] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:47.847] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:47.847] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:47.847] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:52.845] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:52.846] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:52.846] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:52.846] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:52.846] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:52.846] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:52.847] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:09:57.847] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:09:57.847] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:09:57.848] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:09:57.848] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:09:57.848] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:09:57.848] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:09:57.848] Extension: SKIP - Window MethodBodyDlg already has AI Panel
