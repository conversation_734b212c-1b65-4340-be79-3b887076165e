[2025-06-18 15:39:44.093] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:39:44.098] Extension: Initializing AI services...
[2025-06-18 15:39:44.126] Extension: AI Service initialized: True
[2025-06-18 15:39:44.126] Extension: Initialization completed successfully
[2025-06-18 15:39:44.393] Extension: Window monitoring started
[2025-06-18 15:39:44.778] === DnSpyAI Extension Started ===
[2025-06-18 15:39:44.779] Current Time: 18.06.2025 15:39:44
[2025-06-18 15:39:44.779] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:39:44.779] DebugHelper constructor completed successfully
[2025-06-18 15:39:44.779] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:39:44.780] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:39:49.393] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:39:49.393] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:39:49.394] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:39:49.394] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:39:49.394] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:39:49.394] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:39:49.394] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:39:49.394] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:39:49.394] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:39:49.395] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:39:49.395] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:39:49.395] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:39:49.399] Extension: Window content type: Grid
[2025-06-18 15:39:49.399] Extension: AI Panel added to Grid
[2025-06-18 15:39:49.399] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:39:54.389] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:39:54.389] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:39:54.389] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:39:54.390] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:39:54.390] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:39:54.390] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:39:54.390] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:39:59.407] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:39:59.407] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:39:59.407] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:39:59.408] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:39:59.408] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:39:59.408] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:39:59.408] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:04.404] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:04.405] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:04.406] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:04.406] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:04.406] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:04.407] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:04.407] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:09.425] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:09.426] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:09.426] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:09.426] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:09.426] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:09.427] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:09.427] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:14.432] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:14.433] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:14.433] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:14.433] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:14.433] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:14.434] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:14.434] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:17.789] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:40:17.794] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:40:19.446] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:19.446] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:19.447] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:19.447] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:19.447] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:19.447] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:19.448] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:24.435] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:24.435] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:24.435] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:24.435] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:24.435] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:24.435] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:24.435] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:26.437] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:40:26.460] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:40:29.446] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:29.446] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:29.446] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:29.446] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:29.446] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:29.447] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:29.447] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:34.442] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:34.442] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:34.442] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:34.443] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:34.443] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:34.443] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:34.443] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:39.440] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:39.441] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:39.441] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:39.441] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:39.441] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:39.442] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:39.442] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:44.448] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:44.448] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:44.448] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:44.448] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:44.448] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:40:44.448] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:40:44.449] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:40:48.417] Extension: Cleaning up AI Panel on window close
[2025-06-18 15:40:49.449] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:40:49.450] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:40:49.450] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:40:49.450] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:40:49.450] Extension: WARNING - No DataContext, but trying delayed injection...
