[2025-06-18 15:45:02.449] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:45:02.454] Extension: Initializing AI services...
[2025-06-18 15:45:02.484] Extension: AI Service initialized: True
[2025-06-18 15:45:02.484] Extension: Initialization completed successfully
[2025-06-18 15:45:02.776] Extension: Window monitoring started
[2025-06-18 15:45:03.163] === DnSpyAI Extension Started ===
[2025-06-18 15:45:03.163] Current Time: 18.06.2025 15:45:03
[2025-06-18 15:45:03.163] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:45:03.163] DebugHelper constructor completed successfully
[2025-06-18 15:45:03.164] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:45:03.164] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:45:07.768] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:07.769] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:07.769] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:07.770] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:07.770] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:07.770] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:07.770] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:45:07.770] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:45:07.771] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:45:07.771] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:45:07.771] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:45:07.771] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:45:07.775] Extension: Window content type: Grid
[2025-06-18 15:45:07.776] Extension: AI Panel added to Grid
[2025-06-18 15:45:07.776] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:45:12.764] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:12.765] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:12.765] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:12.766] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:12.766] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:12.766] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:12.767] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:17.782] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:17.782] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:17.782] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:17.782] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:17.783] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:17.783] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:17.783] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:22.789] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:22.790] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:22.790] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:22.790] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:22.790] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:22.790] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:22.790] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:26.384] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:45:26.390] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:45:27.779] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:27.780] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:27.780] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:27.780] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:27.780] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:27.780] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:27.780] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:32.779] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:32.779] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:32.779] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:32.780] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:32.780] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:32.780] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:32.780] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:36.718] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:45:36.793] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:45:37.792] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:37.792] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:37.792] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:37.792] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:37.792] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:37.793] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:37.793] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:42.785] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:42.786] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:42.786] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:42.786] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:42.786] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:42.786] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:42.786] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:47.800] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:45:47.800] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:45:47.800] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:45:47.800] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:45:47.801] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:45:47.801] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:45:47.801] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:45:50.642] Extension: Cleaning up AI Panel on window close
