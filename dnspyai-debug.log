[2025-06-18 16:31:00.213] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 16:31:00.218] Extension: Initializing AI services...
[2025-06-18 16:31:00.246] Extension: AI Service initialized: True
[2025-06-18 16:31:00.246] Extension: Initialization completed successfully
[2025-06-18 16:31:00.510] Extension: Window monitoring started
[2025-06-18 16:31:00.889] === DnSpyAI Extension Started ===
[2025-06-18 16:31:00.889] Current Time: 18.06.2025 16:31:00
[2025-06-18 16:31:00.889] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 16:31:00.889] DebugHelper constructor completed successfully
[2025-06-18 16:31:00.890] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 16:31:00.890] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 16:31:05.502] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:05.503] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:05.503] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:05.504] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:05.504] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:05.504] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:05.504] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 16:31:05.504] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 16:31:05.505] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 16:31:05.505] Extension: Found DataContext: MethodBodyVM
[2025-06-18 16:31:05.505] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 16:31:05.506] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 16:31:05.514] Extension: Window content type: Grid
[2025-06-18 16:31:05.514] Extension: AI Panel added to Grid
[2025-06-18 16:31:05.515] Extension: AI Panel successfully injected and tracked!
[2025-06-18 16:31:10.497] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:10.497] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:10.498] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:10.498] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:10.498] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:10.499] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:10.499] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:31:15.503] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:15.503] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:15.503] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:15.504] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:15.504] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:15.505] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:15.505] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:31:20.531] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:20.532] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:20.532] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:20.533] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:20.533] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:20.533] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:20.533] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:31:25.535] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:25.535] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:25.536] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:25.536] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:25.536] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:25.536] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:25.536] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:31:27.705] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 16:31:27.710] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 16:31:30.528] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:30.528] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:30.529] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:30.529] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:30.529] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:30.529] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:30.529] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:31:35.099] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 16:31:35.120] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 16:31:35.547] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:35.548] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:35.548] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:35.548] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:35.548] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:35.549] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:31:35.549] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:31:37.361] Extension: Cleaning up AI Panel on window close
[2025-06-18 16:31:40.560] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:40.560] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:40.560] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:40.560] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:40.560] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:45.570] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:45.570] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:45.570] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:45.571] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:45.571] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:50.567] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:50.568] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:50.568] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:50.568] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:50.568] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:31:55.566] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:31:55.566] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:31:55.567] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:31:55.567] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:31:55.568] Extension: WARNING - No DataContext, but trying delayed injection...
