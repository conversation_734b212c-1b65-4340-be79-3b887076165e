[2025-06-18 15:26:49.553] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:26:49.558] Extension: Initializing AI services...
[2025-06-18 15:26:49.593] Extension: AI Service initialized: True
[2025-06-18 15:26:49.593] Extension: Initialization completed successfully
[2025-06-18 15:26:49.880] Extension: Window monitoring started
[2025-06-18 15:26:50.303] === DnSpyAI Extension Started ===
[2025-06-18 15:26:50.303] Current Time: 18.06.2025 15:26:50
[2025-06-18 15:26:50.303] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:26:50.304] DebugHelper constructor completed successfully
[2025-06-18 15:26:50.304] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:26:50.304] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:26:54.879] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:26:54.879] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:26:54.880] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:26:54.880] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:26:54.880] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:26:54.881] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:26:54.881] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:26:54.881] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:26:54.881] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:26:54.882] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:26:54.882] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:26:54.882] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:26:54.890] Extension: Window content type: Grid
[2025-06-18 15:26:54.891] Extension: AI Panel added to Grid
[2025-06-18 15:26:54.891] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:26:59.887] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:26:59.888] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:26:59.888] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:26:59.888] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:26:59.889] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:26:59.889] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:26:59.889] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:27:04.935] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:27:04.935] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:27:04.935] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:27:04.935] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:27:04.936] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:27:04.936] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:27:04.936] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:27:09.939] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:27:09.939] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:27:09.940] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:27:09.940] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:27:09.940] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:27:09.940] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:27:09.941] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:27:13.158] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:27:13.169] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:27:14.947] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:27:14.947] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:27:14.948] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:27:14.948] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:27:14.948] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:27:14.948] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:27:14.949] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:27:19.679] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:27:19.712] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:27:19.967] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:27:19.968] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:27:19.968] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:27:19.968] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:27:19.968] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:27:19.968] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:27:19.968] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:27:22.778] Extension: Cleaning up AI Panel on window close
