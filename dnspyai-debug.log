[2025-06-18 15:58:04.088] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:04.088] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:04.088] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:04.089] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:04.089] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:09.083] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:09.084] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:09.084] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:09.085] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:09.085] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:09.085] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:09.085] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:58:09.085] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:58:09.086] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:58:09.086] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:58:09.087] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:58:09.087] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:58:09.093] Extension: Window content type: Grid
[2025-06-18 15:58:09.094] Extension: AI Panel added to Grid
[2025-06-18 15:58:09.094] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:58:14.080] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:14.081] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:14.081] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:14.081] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:14.081] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:14.082] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:14.082] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:19.083] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:19.083] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:19.083] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:19.083] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:19.084] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:19.084] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:19.084] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:24.079] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:24.079] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:24.080] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:24.080] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:24.080] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:24.081] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:24.081] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:25.162] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:58:25.167] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:58:29.077] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:29.078] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:29.078] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:29.078] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:29.078] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:29.079] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:29.079] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:33.838] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:58:33.865] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:58:34.077] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:34.078] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:34.078] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:34.078] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:34.078] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:34.079] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:34.079] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:39.075] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:39.075] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:39.075] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:39.076] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:39.076] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:39.076] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:39.076] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:44.084] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:44.084] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:44.084] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:44.084] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:44.084] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:44.084] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:44.085] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:49.075] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:49.075] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:49.076] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:49.076] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:49.076] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:49.076] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:49.077] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:54.105] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:54.105] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:54.106] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:54.106] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:54.106] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:54.107] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:54.107] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:58:59.104] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:58:59.104] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:58:59.104] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:58:59.105] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:58:59.105] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:58:59.105] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:58:59.106] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:04.122] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:04.123] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:04.123] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:04.123] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:04.123] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:04.123] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:04.123] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:09.125] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:09.125] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:09.126] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:09.126] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:09.126] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:09.126] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:09.126] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:14.127] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:14.128] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:14.128] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:14.128] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:14.128] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:14.129] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:14.129] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:19.125] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:19.125] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:19.125] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:19.126] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:19.126] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:19.126] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:19.126] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:24.125] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:24.125] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:24.125] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:24.126] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:24.126] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:24.126] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:24.126] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:29.128] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:29.128] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:29.128] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:29.128] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:29.128] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:29.128] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:29.128] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:34.151] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:34.152] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:34.152] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:34.152] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:34.152] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:34.153] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:34.153] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:39.164] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:39.164] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:39.164] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:39.165] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:39.165] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:39.165] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:39.165] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:44.167] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:44.167] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:44.168] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:44.168] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:44.168] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:44.168] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:44.168] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:49.160] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:49.160] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:49.160] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:49.161] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:49.161] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:49.161] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:49.161] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:54.178] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:54.178] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:54.178] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:54.178] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:54.178] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:54.179] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:54.179] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:59:59.169] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:59:59.169] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:59:59.170] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:59:59.170] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:59:59.170] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:59:59.170] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:59:59.170] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:04.180] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:04.180] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:04.180] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:04.180] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:04.180] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:04.181] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:04.181] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:09.174] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:09.174] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:09.174] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:09.175] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:09.175] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:09.176] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:09.176] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:14.174] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:14.174] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:14.174] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:14.175] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:14.175] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:14.175] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:14.176] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:19.172] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:19.173] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:19.173] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:19.173] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:19.173] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:19.174] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:19.174] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:24.173] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:24.173] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:24.173] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:24.174] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:24.174] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:24.174] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:24.174] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:29.176] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:29.176] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:29.176] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:29.176] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:29.177] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:29.177] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:29.177] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:34.177] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:34.178] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:34.178] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:34.178] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:34.179] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:34.179] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:34.179] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:39.203] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:39.204] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:39.204] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:39.204] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:39.205] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:39.205] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:39.205] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:44.204] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:44.204] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:44.205] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:44.205] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:44.205] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:44.205] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:44.205] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:49.211] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:49.211] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:49.211] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:49.212] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:49.212] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:49.212] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:49.212] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:54.210] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:54.210] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:54.211] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:54.211] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:54.211] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:54.212] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:54.212] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:00:59.208] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:00:59.208] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:00:59.209] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:00:59.209] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:00:59.210] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:00:59.210] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:00:59.210] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:04.227] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:04.227] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:04.227] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:04.228] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:04.228] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:04.228] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:04.228] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:09.217] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:09.217] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:09.217] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:09.217] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:09.218] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:09.218] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:09.218] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:14.232] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:14.232] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:14.232] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:14.232] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:14.233] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:14.233] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:14.234] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:19.244] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:19.245] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:19.245] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:19.245] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:19.245] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:19.246] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:19.246] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:24.245] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:24.246] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:24.246] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:24.246] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:24.246] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:24.246] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:24.246] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:29.231] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:29.231] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:29.232] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:29.232] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:29.232] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:29.232] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:29.233] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:01:34.242] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:01:34.243] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:01:34.243] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:01:34.243] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:01:34.243] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:01:34.244] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:01:34.244] Extension: SKIP - Window MethodBodyDlg already has AI Panel
