[2025-06-18 15:37:05.346] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:37:05.351] Extension: Initializing AI services...
[2025-06-18 15:37:05.378] Extension: AI Service initialized: True
[2025-06-18 15:37:05.378] Extension: Initialization completed successfully
[2025-06-18 15:37:05.650] Extension: Window monitoring started
[2025-06-18 15:37:06.001] === DnSpyAI Extension Started ===
[2025-06-18 15:37:06.001] Current Time: 18.06.2025 15:37:06
[2025-06-18 15:37:06.001] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:37:06.001] DebugHelper constructor completed successfully
[2025-06-18 15:37:06.001] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:37:06.002] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:37:10.644] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:10.644] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:10.645] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:10.645] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:10.645] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:10.645] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:10.645] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:37:10.645] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:37:10.645] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:37:10.645] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:37:10.646] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:37:10.646] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:37:10.650] Extension: Window content type: Grid
[2025-06-18 15:37:10.650] Extension: AI Panel added to Grid
[2025-06-18 15:37:10.650] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:37:15.646] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:15.646] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:15.646] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:15.646] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:15.646] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:15.647] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:15.647] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:20.664] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:20.664] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:20.664] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:20.664] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:20.665] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:20.665] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:20.665] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:25.667] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:25.669] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:25.669] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:25.669] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:25.669] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:25.669] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:25.669] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:30.676] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:30.676] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:30.676] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:30.677] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:30.677] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:30.677] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:30.677] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:31.567] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:37:31.575] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:37:35.750] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:35.751] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:35.751] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:35.751] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:35.752] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:35.752] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:35.752] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:39.499] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:37:39.525] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:37:40.759] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:40.759] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:40.759] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:40.759] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:40.759] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:40.759] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:40.759] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:45.769] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:45.770] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:45.770] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:45.770] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:45.770] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:45.771] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:45.771] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:50.783] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:37:50.783] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:37:50.784] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:37:50.784] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:37:50.784] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:37:50.785] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:37:50.785] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:37:52.073] Extension: Cleaning up AI Panel on window close
