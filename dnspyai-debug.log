[2025-06-18 15:34:06.161] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:06.161] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:06.161] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:06.161] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:06.161] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:11.162] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:11.163] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:11.163] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:11.163] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:11.163] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:11.163] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:11.164] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:34:11.164] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:34:11.164] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:34:11.164] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:34:11.165] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:34:11.165] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:34:11.169] Extension: Window content type: Grid
[2025-06-18 15:34:11.169] Extension: AI Panel added to Grid
[2025-06-18 15:34:11.169] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:34:16.163] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:16.163] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:16.163] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:16.163] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:16.164] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:16.164] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:16.164] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:21.161] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:21.162] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:21.162] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:21.162] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:21.163] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:21.163] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:21.163] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:26.155] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:26.155] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:26.155] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:26.155] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:26.156] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:26.156] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:26.156] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:26.911] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:34:26.918] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:34:31.161] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:31.161] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:31.161] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:31.161] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:31.161] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:31.162] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:31.162] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:34.779] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:34:34.808] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:34:36.162] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:36.162] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:36.162] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:36.162] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:36.162] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:36.162] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:36.162] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:41.162] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:41.163] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:41.163] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:41.163] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:41.163] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:41.163] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:41.163] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:46.160] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:46.160] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:46.160] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:46.160] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:46.161] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:46.161] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:46.161] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:51.162] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:34:51.162] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:34:51.162] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:34:51.162] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:34:51.163] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:34:51.163] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:34:51.163] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:34:51.454] Extension: Cleaning up AI Panel on window close
