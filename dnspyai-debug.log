[2025-06-18 16:34:24.724] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 16:34:24.729] Extension: Initializing AI services...
[2025-06-18 16:34:24.757] Extension: AI Service initialized: True
[2025-06-18 16:34:24.757] Extension: Initialization completed successfully
[2025-06-18 16:34:25.016] Extension: Window monitoring started
[2025-06-18 16:34:25.442] === DnSpyAI Extension Started ===
[2025-06-18 16:34:25.442] Current Time: 18.06.2025 16:34:25
[2025-06-18 16:34:25.442] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 16:34:25.443] DebugHelper constructor completed successfully
[2025-06-18 16:34:25.443] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 16:34:25.444] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 16:34:30.040] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:34:30.040] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:34:30.041] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:34:30.041] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:34:30.041] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:34:30.041] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:34:30.042] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 16:34:30.042] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 16:34:30.042] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 16:34:30.042] Extension: Found DataContext: MethodBodyVM
[2025-06-18 16:34:30.043] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 16:34:30.043] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 16:34:30.047] Extension: Window content type: Grid
[2025-06-18 16:34:30.047] Extension: AI Panel added to Grid
[2025-06-18 16:34:30.047] Extension: AI Panel successfully injected and tracked!
[2025-06-18 16:34:35.034] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:34:35.035] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:34:35.035] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:34:35.035] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:34:35.035] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:34:35.036] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:34:35.036] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:34:40.036] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:34:40.037] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:34:40.037] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:34:40.037] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:34:40.037] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:34:40.037] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:34:40.037] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:34:45.043] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:34:45.043] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:34:45.043] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:34:45.044] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:34:45.044] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:34:45.044] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:34:45.044] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:34:47.142] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 16:34:47.150] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 16:34:50.045] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:34:50.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:34:50.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:34:50.045] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:34:50.046] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:34:50.046] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:34:50.046] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:34:53.476] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 16:34:53.498] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 16:34:55.066] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:34:55.066] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:34:55.066] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:34:55.067] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:34:55.067] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:34:55.067] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:34:55.068] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:34:58.854] Extension: Cleaning up AI Panel on window close
[2025-06-18 16:35:00.065] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:00.065] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:00.065] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:00.065] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:00.066] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:05.058] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:05.059] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:05.059] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:05.059] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:05.059] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:05.059] Extension: Checking window: EditCodeDlg - Title: 'Edit Code - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.Compiler.EditCodeDlg
[2025-06-18 16:35:05.059] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.Compiler.EditCodeDlg - Title: 'Edit Code - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 16:35:05.060] Extension: Window EditCodeDlg has NO AI Panel
[2025-06-18 16:35:05.060] Extension: Attempting to inject AI panel into: EditCodeDlg
[2025-06-18 16:35:05.060] Extension: Found DataContext: EditMethodCodeVM
[2025-06-18 16:35:05.060] Extension: Analyzing DataContext: EditMethodCodeVM (Full: dnSpy.AsmEditor.Compiler.EditMethodCodeVM)
[2025-06-18 16:35:05.060] Extension: *** CONFIRMED EDIT CONTEXT *** : EditMethodCodeVM
[2025-06-18 16:35:05.138] Extension: Window content type: Grid
[2025-06-18 16:35:05.138] Extension: AI Panel added to Grid
[2025-06-18 16:35:05.138] Extension: AI Panel successfully injected and tracked!
[2025-06-18 16:35:10.131] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:10.131] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:10.131] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:10.132] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:10.132] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:10.132] Extension: Checking window: EditCodeDlg - Title: 'Edit Code - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.Compiler.EditCodeDlg
[2025-06-18 16:35:10.132] Extension: SKIP - Window EditCodeDlg already has AI Panel
[2025-06-18 16:35:15.129] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:15.129] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:15.129] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:15.129] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:15.130] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:15.130] Extension: Checking window: EditCodeDlg - Title: 'Edit Code - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.Compiler.EditCodeDlg
[2025-06-18 16:35:15.130] Extension: SKIP - Window EditCodeDlg already has AI Panel
[2025-06-18 16:35:15.826] Found C# Editor (PRIORITY 2): EditCodeDlg, DataContext: EditMethodCodeVM
[2025-06-18 16:35:15.829] AIEditPanel: Found real code via AIAgent: 565 chars
[2025-06-18 16:35:15.829] Found C# Editor (PRIORITY 2): EditCodeDlg, DataContext: EditMethodCodeVM
[2025-06-18 16:35:15.830] AIEditPanel: Found real code via AIAgent: 565 chars
[2025-06-18 16:35:20.126] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:20.126] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:20.126] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:20.127] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:20.127] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:20.127] Extension: Checking window: EditCodeDlg - Title: 'Edit Code - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.Compiler.EditCodeDlg
[2025-06-18 16:35:20.127] Extension: SKIP - Window EditCodeDlg already has AI Panel
[2025-06-18 16:35:25.045] Extension: Cleaning up AI Panel on window close
[2025-06-18 16:35:25.130] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:25.130] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:25.130] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:25.131] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:25.131] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:30.153] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:30.154] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:30.154] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:30.154] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:30.154] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:35:35.166] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:35:35.167] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:35:35.167] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:35:35.167] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:35:35.168] Extension: WARNING - No DataContext, but trying delayed injection...
