[2025-06-18 16:12:09.504] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 16:12:09.508] Extension: Initializing AI services...
[2025-06-18 16:12:09.535] Extension: AI Service initialized: True
[2025-06-18 16:12:09.535] Extension: Initialization completed successfully
[2025-06-18 16:12:09.806] Extension: Window monitoring started
[2025-06-18 16:12:10.165] === DnSpyAI Extension Started ===
[2025-06-18 16:12:10.166] Current Time: 18.06.2025 16:12:10
[2025-06-18 16:12:10.166] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 16:12:10.166] DebugHelper constructor completed successfully
[2025-06-18 16:12:10.166] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 16:12:10.166] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 16:12:14.793] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:14.793] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:14.794] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:14.794] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:14.794] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:14.795] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:14.795] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 16:12:14.795] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 16:12:14.795] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 16:12:14.795] Extension: Found DataContext: MethodBodyVM
[2025-06-18 16:12:14.795] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 16:12:14.795] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 16:12:14.799] Extension: Window content type: Grid
[2025-06-18 16:12:14.800] Extension: AI Panel added to Grid
[2025-06-18 16:12:14.800] Extension: AI Panel successfully injected and tracked!
[2025-06-18 16:12:19.808] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:19.808] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:19.808] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:19.808] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:19.808] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:19.809] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:19.809] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:24.809] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:24.810] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:24.810] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:24.810] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:24.810] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:24.811] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:24.811] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:29.815] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:29.815] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:29.816] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:29.816] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:29.816] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:29.816] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:29.817] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:34.809] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:34.810] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:34.810] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:34.810] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:34.810] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:34.810] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:34.811] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:36.226] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 16:12:36.236] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 16:12:39.811] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:39.812] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:39.812] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:39.812] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:39.812] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:39.812] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:39.812] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:44.823] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:44.824] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:44.824] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:44.824] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:44.824] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:44.824] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:44.824] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:45.496] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 16:12:45.518] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 16:12:49.828] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:49.828] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:49.829] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:49.829] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:49.829] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:49.829] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:49.830] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:54.827] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:54.827] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:54.828] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:54.828] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:54.828] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:54.828] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:54.828] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:12:59.826] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:12:59.826] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:12:59.826] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:12:59.827] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:12:59.827] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:12:59.827] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:12:59.827] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:13:04.828] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:13:04.829] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:13:04.829] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:13:04.829] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:13:04.829] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:13:04.829] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:13:04.829] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:13:09.827] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 16:13:09.828] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 16:13:09.828] Extension: Window MainWindow has NO AI Panel
[2025-06-18 16:13:09.828] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 16:13:09.829] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 16:13:09.829] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 16:13:09.829] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 16:13:12.473] Extension: Cleaning up AI Panel on window close
