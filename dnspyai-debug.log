[2025-06-18 15:11:49.618] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:11:49.622] Extension: Initializing AI services...
[2025-06-18 15:11:49.649] Extension: AI Service initialized: True
[2025-06-18 15:11:49.649] Extension: Initialization completed successfully
[2025-06-18 15:11:49.890] Extension: Window monitoring started
[2025-06-18 15:11:50.289] === DnSpyAI Extension Started ===
[2025-06-18 15:11:50.289] Current Time: 18.06.2025 15:11:50
[2025-06-18 15:11:50.290] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:11:50.290] DebugHelper constructor completed successfully
[2025-06-18 15:11:50.290] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:11:50.290] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:11:54.897] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:11:54.898] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:11:54.898] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:11:54.899] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:11:54.899] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:11:54.899] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:11:54.899] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:11:54.899] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:11:54.899] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:11:54.899] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:11:54.899] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:11:54.900] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:11:54.903] Extension: Window content type: Grid
[2025-06-18 15:11:54.904] Extension: AI Panel added to Grid
[2025-06-18 15:11:54.904] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:11:59.899] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:11:59.899] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:11:59.899] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:11:59.900] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:11:59.900] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:11:59.900] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:11:59.900] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:04.888] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:04.888] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:04.888] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:04.889] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:04.889] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:04.889] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:04.890] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:09.900] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:09.901] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:09.901] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:09.901] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:09.901] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:09.902] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:09.902] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:14.888] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:14.888] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:14.888] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:14.888] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:14.888] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:14.888] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:14.888] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:16.880] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:12:16.885] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:12:19.897] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:19.897] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:19.897] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:19.898] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:19.898] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:19.898] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:19.898] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:24.901] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:24.902] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:24.902] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:24.902] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:24.902] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:24.902] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:24.902] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:29.896] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:29.897] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:29.897] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:29.897] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:29.897] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:29.897] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:29.897] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:34.816] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:12:34.841] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:12:34.911] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:34.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:34.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:34.912] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:34.912] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:34.912] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:34.912] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:39.911] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:39.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:39.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:39.912] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:39.912] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:39.913] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:39.913] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:44.912] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:44.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:44.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:44.912] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:44.912] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:44.913] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:44.913] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:49.905] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:49.905] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:49.906] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:49.906] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:49.906] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:49.906] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:49.907] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:54.913] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:54.913] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:54.913] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:54.914] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:54.914] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:54.914] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:54.914] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:12:59.911] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:12:59.911] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:12:59.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:12:59.912] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:12:59.912] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:12:59.913] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:12:59.913] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:13:04.912] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:13:04.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:13:04.913] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:13:04.913] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:13:04.913] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:13:04.913] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:13:04.914] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:13:09.912] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:13:09.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:13:09.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:13:09.913] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:13:09.913] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:13:09.913] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:13:09.913] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:13:14.911] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:13:14.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:13:14.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:13:14.912] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:13:14.912] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:13:14.912] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:13:14.912] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:13:19.903] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:13:19.903] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:13:19.904] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:13:19.904] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:13:19.904] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:13:19.904] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:13:19.904] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:13:24.912] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:13:24.912] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:13:24.912] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:13:24.913] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:13:24.913] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:13:24.913] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:13:24.913] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:13:29.913] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:13:29.913] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:13:29.914] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:13:29.914] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:13:29.914] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:13:29.914] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:13:29.915] Extension: SKIP - Window MethodBodyDlg already has AI Panel
