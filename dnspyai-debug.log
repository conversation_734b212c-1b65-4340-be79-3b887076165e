[2025-06-18 15:53:40.653] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:53:40.658] Extension: Initializing AI services...
[2025-06-18 15:53:40.686] Extension: AI Service initialized: True
[2025-06-18 15:53:40.686] Extension: Initialization completed successfully
[2025-06-18 15:53:40.953] Extension: Window monitoring started
[2025-06-18 15:53:41.376] === DnSpyAI Extension Started ===
[2025-06-18 15:53:41.377] Current Time: 18.06.2025 15:53:41
[2025-06-18 15:53:41.377] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:53:41.377] DebugHelper constructor completed successfully
[2025-06-18 15:53:41.378] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:53:41.378] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:53:45.985] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:53:45.986] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:53:45.986] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:53:45.987] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:53:45.987] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:53:45.987] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:53:45.987] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:53:45.987] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:53:45.987] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:53:45.988] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:53:45.988] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:53:45.988] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:53:45.996] Extension: Window content type: Grid
[2025-06-18 15:53:45.997] Extension: AI Panel added to Grid
[2025-06-18 15:53:45.997] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:53:50.986] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:53:50.986] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:53:50.986] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:53:50.987] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:53:50.987] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:53:50.987] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:53:50.987] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:53:55.984] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:53:55.985] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:53:55.985] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:53:55.985] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:53:55.985] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:53:55.985] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:53:55.985] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:54:00.954] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:54:00.961] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:54:01.111] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:54:01.112] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:54:01.112] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:54:01.112] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:54:01.113] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:54:01.113] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:54:01.113] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:54:06.113] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:54:06.113] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:54:06.113] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:54:06.114] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:54:06.114] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:54:06.114] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:54:06.114] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:54:08.304] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:54:08.328] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:54:11.115] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:54:11.115] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:54:11.115] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:54:11.115] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:54:11.116] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:54:11.116] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:54:11.116] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:54:16.113] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:54:16.114] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:54:16.114] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:54:16.114] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:54:16.115] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:54:16.115] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:54:16.115] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:54:21.112] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:54:21.112] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:54:21.112] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:54:21.113] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:54:21.113] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:54:21.113] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:54:21.113] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:54:22.634] Extension: Cleaning up AI Panel on window close
