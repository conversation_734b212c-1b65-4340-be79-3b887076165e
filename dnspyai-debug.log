[2025-06-18 14:43:42.608] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 14:43:42.613] Extension: Initializing AI services...
[2025-06-18 14:43:42.662] Extension: AI Service initialized: True
[2025-06-18 14:43:42.662] Extension: Initialization completed successfully
[2025-06-18 14:43:42.993] Extension: Window monitoring started
[2025-06-18 14:43:43.328] === DnSpyAI Extension Started ===
[2025-06-18 14:43:43.328] Current Time: 18.06.2025 14:43:43
[2025-06-18 14:43:43.328] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 14:43:43.329] DebugHelper constructor completed successfully
[2025-06-18 14:43:43.332] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 14:43:43.332] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 14:43:47.993] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:43:47.993] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:43:47.994] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:43:47.994] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:43:47.994] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:43:52.990] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:43:52.990] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:43:52.990] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:43:52.991] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:43:52.991] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:43:57.985] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:43:57.985] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:43:57.985] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:43:57.986] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:43:57.986] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:02.989] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:02.990] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:02.990] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:02.990] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:02.990] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:07.992] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:07.993] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:07.993] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:07.993] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:07.993] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:12.987] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:12.988] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:12.988] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:12.988] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:12.988] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:17.984] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:17.984] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:17.984] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:17.984] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:17.985] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:22.991] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:22.991] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:22.991] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:22.991] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:22.991] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:27.992] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:27.992] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:27.993] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:27.993] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:27.993] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:32.989] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:32.989] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:32.989] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:32.989] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:32.989] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:37.985] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:37.986] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:37.986] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:37.986] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:37.986] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:42.988] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:42.988] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:42.989] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:42.989] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:42.989] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:42.989] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:44:42.989] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 14:44:42.989] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 14:44:42.989] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 14:44:42.989] Extension: Found DataContext: MethodBodyVM
[2025-06-18 14:44:42.990] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 14:44:42.990] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 14:44:42.993] Extension: Window content type: Grid
[2025-06-18 14:44:42.993] Extension: AI Panel added to Grid
[2025-06-18 14:44:42.993] Extension: AI Panel successfully injected and tracked!
[2025-06-18 14:44:47.986] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:47.986] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:47.986] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:47.986] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:47.987] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:47.987] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:44:47.987] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:44:53.011] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:53.011] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:53.011] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:53.011] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:53.011] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:53.011] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:44:53.011] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:44:58.013] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:44:58.013] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:44:58.013] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:44:58.014] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:44:58.014] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:44:58.014] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:44:58.014] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:01.358] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 14:45:01.364] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 14:45:03.014] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:03.014] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:03.014] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:03.014] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:03.014] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:03.014] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:03.014] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:08.012] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:08.012] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:08.012] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:08.012] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:08.012] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:08.013] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:08.013] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:13.030] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:13.030] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:13.030] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:13.030] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:13.030] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:13.030] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:13.030] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:16.194] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 14:45:16.229] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 14:45:18.034] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:18.034] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:18.034] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:18.034] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:18.034] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:18.035] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:18.035] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:23.036] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:23.036] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:23.036] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:23.036] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:23.036] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:23.036] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:23.036] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:28.030] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:28.031] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:28.031] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:28.031] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:28.031] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:28.031] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:28.031] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:33.040] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:33.040] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:33.040] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:33.040] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:33.040] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:33.041] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:33.041] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:38.030] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:38.030] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:38.030] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:38.030] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:38.030] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:38.031] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:38.031] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:43.033] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:43.034] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:43.034] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:43.034] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:43.034] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:43.035] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:43.035] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:48.027] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:48.027] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:48.027] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:48.027] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:48.028] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:48.028] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:48.028] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:53.034] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:53.035] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:53.035] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:53.035] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:53.035] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:53.035] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:53.035] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:45:58.028] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:45:58.028] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:45:58.029] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:45:58.029] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:45:58.029] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:45:58.029] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:45:58.030] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:03.044] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:03.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:03.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:03.045] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:03.045] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:03.045] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:03.045] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:08.060] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:08.060] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:08.060] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:08.060] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:08.060] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:08.060] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:08.061] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:13.047] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:13.048] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:13.048] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:13.048] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:13.048] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:13.048] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:13.048] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:18.044] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:18.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:18.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:18.045] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:18.045] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:18.045] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:18.045] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:23.045] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:23.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:23.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:23.045] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:23.045] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:23.046] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:23.046] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:28.045] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:28.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:28.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:28.046] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:28.046] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:28.046] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:28.046] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:33.052] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:33.053] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:33.053] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:33.053] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:33.053] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:33.053] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:33.053] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:38.047] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:38.047] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:38.047] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:38.048] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:38.048] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:38.048] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:38.048] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:43.048] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:43.049] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:43.049] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:43.049] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:43.049] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:43.049] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:43.049] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:48.049] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:48.049] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:48.049] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:48.049] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:48.050] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:48.050] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:48.050] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:53.048] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:53.049] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:53.049] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:53.049] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:53.049] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:53.049] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:53.050] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:46:58.049] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:46:58.049] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:46:58.049] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:46:58.049] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:46:58.050] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:46:58.050] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:46:58.050] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:03.045] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:03.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:03.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:03.045] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:03.045] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:03.046] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:03.046] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:08.045] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:08.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:08.046] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:08.046] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:08.046] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:08.046] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:08.046] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:13.043] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:13.043] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:13.043] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:13.043] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:13.044] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:13.044] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:13.044] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:18.056] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:18.056] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:18.057] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:18.057] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:18.057] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:18.057] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:18.057] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:23.055] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:23.055] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:23.055] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:23.055] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:23.056] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:23.056] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:23.056] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:28.047] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:28.047] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:28.048] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:28.048] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:28.048] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:28.048] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:28.048] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:33.048] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:33.048] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:33.048] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:33.048] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:33.048] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:33.049] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:33.049] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:38.047] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:38.048] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:38.048] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:38.048] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:38.048] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:38.048] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:38.048] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:43.056] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:43.056] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:43.056] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:43.056] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:43.056] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:43.056] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:43.056] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:48.059] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:48.059] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:48.059] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:48.060] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:48.060] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:48.060] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:48.060] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:53.061] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:53.061] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:53.062] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:53.062] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:53.062] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:53.062] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:53.062] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:47:58.064] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:47:58.064] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:47:58.065] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:47:58.065] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:47:58.065] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 14:47:58.065] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 14:47:58.065] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 14:48:02.717] Extension: Cleaning up AI Panel on window close
[2025-06-18 14:48:03.064] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 14:48:03.064] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 14:48:03.064] Extension: Window MainWindow has NO AI Panel
[2025-06-18 14:48:03.064] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 14:48:03.064] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:00:53.693] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:00:53.697] Extension: Initializing AI services...
[2025-06-18 15:00:53.728] Extension: AI Service initialized: True
[2025-06-18 15:00:53.729] Extension: Initialization completed successfully
[2025-06-18 15:00:53.972] Extension: Window monitoring started
[2025-06-18 15:00:54.398] === DnSpyAI Extension Started ===
[2025-06-18 15:00:54.400] Current Time: 18.06.2025 15:00:54
[2025-06-18 15:00:54.400] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:00:54.400] DebugHelper constructor completed successfully
[2025-06-18 15:00:54.400] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:00:54.400] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:00:58.979] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:00:58.979] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:00:58.980] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:00:58.980] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:00:58.980] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:00:58.980] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:00:58.980] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:00:58.980] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:00:58.980] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:00:58.980] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:00:58.981] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:00:58.981] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:00:58.985] Extension: Window content type: Grid
[2025-06-18 15:00:58.985] Extension: AI Panel added to Grid
[2025-06-18 15:00:58.985] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:01:03.972] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:03.972] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:03.972] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:03.972] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:03.972] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:03.972] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:01:03.973] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:01:08.965] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:08.966] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:08.966] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:08.966] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:08.966] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:08.967] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:01:08.967] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:01:13.974] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:13.975] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:13.975] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:13.975] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:13.975] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:13.976] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:01:13.976] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:01:16.790] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:01:16.800] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:01:18.992] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:18.992] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:18.992] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:18.992] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:18.993] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:18.993] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:01:18.993] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:01:23.992] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:23.993] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:23.993] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:23.993] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:23.993] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:23.994] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:01:23.994] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:01:26.100] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:01:26.102] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:01:28.995] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:28.995] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:28.995] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:28.996] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:28.996] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:28.996] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:01:28.996] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:01:33.917] Extension: Cleaning up AI Panel on window close
[2025-06-18 15:01:33.993] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:33.993] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:33.993] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:33.993] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:33.993] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:39.000] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:39.000] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:39.000] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:39.000] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:39.000] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:44.011] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:44.011] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:44.011] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:44.011] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:44.011] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:49.000] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:01:49.001] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:01:49.001] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:01:49.001] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:01:49.001] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:01:49.001] Extension: Checking window: MsgBoxDlg - Title: 'dnSpy' - FullName: dnSpy.MainApp.MsgBoxDlg
[2025-06-18 15:01:49.001] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MsgBoxDlg - Title: 'dnSpy'
[2025-06-18 15:01:49.002] Extension: Window MsgBoxDlg has NO AI Panel
[2025-06-18 15:01:49.002] Extension: Attempting to inject AI panel into: MsgBoxDlg
[2025-06-18 15:01:49.002] Extension: Found DataContext: MsgBoxVM
[2025-06-18 15:01:49.002] Extension: Analyzing DataContext: MsgBoxVM (Full: dnSpy.MainApp.MsgBoxVM)
[2025-06-18 15:01:49.002] Extension: SKIP - Not an edit context: MsgBoxVM
