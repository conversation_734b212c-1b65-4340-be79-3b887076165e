[2025-06-18 15:04:10.710] === DNSPY AI EXTENSION LOADED ===
[2025-06-18 15:04:10.714] Extension: Initializing AI services...
[2025-06-18 15:04:10.741] Extension: AI Service initialized: True
[2025-06-18 15:04:10.741] Extension: Initialization completed successfully
[2025-06-18 15:04:10.988] Extension: Window monitoring started
[2025-06-18 15:04:11.398] === DnSpyAI Extension Started ===
[2025-06-18 15:04:11.398] Current Time: 18.06.2025 15:04:11
[2025-06-18 15:04:11.398] Extension Assembly: C:\Users\<USER>\Desktop\win-x64\bin\Extensions\DnSpyAI.x.dll
[2025-06-18 15:04:11.398] DebugHelper constructor completed successfully
[2025-06-18 15:04:11.399] EditDialogInterceptor: DISABLED - Starting minimal initialization...
[2025-06-18 15:04:11.399] EditDialogInterceptor: DISABLED - Minimal initialization completed
[2025-06-18 15:04:16.002] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:16.002] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:16.003] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:16.003] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:16.003] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:16.003] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:16.004] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.AsmEditor.MethodBody.MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB'
[2025-06-18 15:04:16.004] Extension: Window MethodBodyDlg has NO AI Panel
[2025-06-18 15:04:16.004] Extension: Attempting to inject AI panel into: MethodBodyDlg
[2025-06-18 15:04:16.004] Extension: Found DataContext: MethodBodyVM
[2025-06-18 15:04:16.004] Extension: Analyzing DataContext: MethodBodyVM (Full: dnSpy.AsmEditor.MethodBody.MethodBodyVM)
[2025-06-18 15:04:16.004] Extension: *** CONFIRMED EDIT CONTEXT *** : MethodBodyVM
[2025-06-18 15:04:16.008] Extension: Window content type: Grid
[2025-06-18 15:04:16.008] Extension: AI Panel added to Grid
[2025-06-18 15:04:16.008] Extension: AI Panel successfully injected and tracked!
[2025-06-18 15:04:20.998] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:20.999] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:20.999] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:20.999] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:20.999] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:21.000] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:21.000] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:26.011] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:26.011] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:26.012] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:26.012] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:26.013] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:26.013] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:26.013] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:31.025] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:31.025] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:31.025] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:31.025] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:31.026] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:31.026] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:31.026] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:31.886] Found IL Editor (PRIORITY 1): MethodBodyDlg, DataContext: MethodBodyVM
[2025-06-18 15:04:31.896] AIEditPanel: Found real code via AIAgent: 1648 chars
[2025-06-18 15:04:36.030] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:36.030] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:36.030] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:36.030] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:36.031] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:36.031] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:36.031] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:38.980] About to call WriteToILEditorWithTwoPhase...
[2025-06-18 15:04:39.000] WriteToILEditorWithTwoPhase completed successfully
[2025-06-18 15:04:41.042] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:41.042] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:41.042] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:41.042] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:41.043] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:41.043] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:41.043] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:46.052] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:46.052] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:46.052] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:46.052] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:46.052] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:46.052] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:46.052] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:51.045] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:51.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:51.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:51.046] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:51.046] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:51.046] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:51.046] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:04:56.044] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:04:56.045] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:04:56.045] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:04:56.045] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:04:56.045] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:04:56.045] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:04:56.045] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:01.060] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:01.060] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:01.061] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:01.061] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:01.061] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:01.061] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:01.061] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:06.061] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:06.061] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:06.062] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:06.062] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:06.062] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:06.062] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:06.062] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:11.069] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:11.070] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:11.070] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:11.070] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:11.071] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:11.071] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:11.071] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:16.067] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:16.067] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:16.067] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:16.068] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:16.068] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:16.068] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:16.069] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:21.081] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:21.081] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:21.081] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:21.081] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:21.081] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:21.081] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:21.082] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:26.083] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:26.083] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:26.084] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:26.084] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:26.084] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:26.085] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:26.085] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:31.079] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:31.080] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:31.080] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:31.080] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:31.080] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:31.081] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:31.081] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:36.076] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:36.076] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:36.076] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:36.076] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:36.077] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:36.077] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:36.077] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:41.079] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:41.079] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:41.079] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:41.079] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:41.079] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:41.080] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:41.080] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:46.088] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:46.089] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:46.089] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:46.089] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:46.089] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:46.089] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:46.089] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:51.084] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:51.084] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:51.084] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:51.084] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:51.085] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:51.085] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:51.085] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:05:56.083] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:05:56.084] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:05:56.084] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:05:56.084] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:05:56.084] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:05:56.084] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:05:56.084] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:06:01.094] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:06:01.094] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:06:01.094] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:06:01.094] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:06:01.094] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:06:01.094] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:06:01.094] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:06:06.094] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:06:06.094] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:06:06.095] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:06:06.095] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:06:06.095] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:06:06.095] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:06:06.096] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:06:11.120] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:06:11.120] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:06:11.120] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:06:11.120] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:06:11.121] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:06:11.121] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:06:11.121] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:06:16.110] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:06:16.111] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:06:16.111] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:06:16.111] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:06:16.111] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:06:16.111] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:06:16.111] Extension: SKIP - Window MethodBodyDlg already has AI Panel
[2025-06-18 15:06:21.113] Extension: Checking window: MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)' - FullName: dnSpy.MainApp.MainWindow
[2025-06-18 15:06:21.113] Extension: *** EDIT DIALOG DETECTED *** - dnSpy.MainApp.MainWindow - Title: 'dnSpy v6.5.1 (64-bit, .NET)'
[2025-06-18 15:06:21.113] Extension: Window MainWindow has NO AI Panel
[2025-06-18 15:06:21.113] Extension: Attempting to inject AI panel into: MainWindow
[2025-06-18 15:06:21.114] Extension: WARNING - No DataContext, but trying delayed injection...
[2025-06-18 15:06:21.114] Extension: Checking window: MethodBodyDlg - Title: 'Edit Method Body - IsValidOnWebFramework(StiLicenseKey) : bool @060012EB' - FullName: dnSpy.AsmEditor.MethodBody.MethodBodyDlg
[2025-06-18 15:06:21.114] Extension: SKIP - Window MethodBodyDlg already has AI Panel
