/*
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
*/

using System;

namespace dnSpy.Documents.TreeView {
	static class DocumentTreeViewAppSettingsConstants {
		/// <summary>
		/// Assembly Explorer member order option got updated
		/// </summary>
		public static readonly Guid REFRESH_ASSEMBLY_EXPLORER_MEMBER_ORDER = new Guid("CA54455B-6D5D-4E82-86D4-7CE4CA5AC478");
	}
}
