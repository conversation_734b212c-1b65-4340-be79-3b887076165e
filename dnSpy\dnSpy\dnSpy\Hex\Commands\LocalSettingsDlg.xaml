<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<winlocal:WindowBase x:Class="dnSpy.Hex.Commands.LocalSettingsDlg"
        x:ClassModifier="internal"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:winlocal="clr-namespace:dnSpy.Contracts.Controls;assembly=dnSpy.Contracts.DnSpy"
        xmlns:mvvmvc="clr-namespace:dnSpy.Contracts.MVVM.Converters;assembly=dnSpy.Contracts.DnSpy"
        xmlns:p="clr-namespace:dnSpy.Properties"
        Width="550"
        SizeToContent="Height"
        ResizeMode="NoResize"
        Title="{x:Static p:dnSpy_Resources.LocalHexSettings_Title}"
        Style="{StaticResource DialogWindowStyle}" WindowStartupLocation="CenterOwner"
        MinWidth="550">
    <winlocal:WindowBase.Resources>
        <mvvmvc:NegateBooleanConverter x:Key="NegateBooleanConverter" />
    </winlocal:WindowBase.Resources>
    <Grid FocusManager.FocusedElement="{Binding ElementName=startPositionTextBox}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <Grid Grid.Row="0" Margin="0 5 5 0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <Label Grid.Row="0" Grid.Column="0" Margin="0 5 0 0" Target="{Binding ElementName=startPositionTextBox}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_StartOffset}" />
            <TextBox Grid.Row="0" Grid.Column="1" Margin="5 5 0 0" Name="startPositionTextBox" Text="{Binding StartPositionVM.StringValue, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" />
            <Label Grid.Row="0" Grid.Column="2" Margin="5 5 0 0" Target="{Binding ElementName=endPositionTextBox}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_EndOffset}" />
            <TextBox Grid.Row="0" Grid.Column="3" Margin="5 5 0 0" Name="endPositionTextBox" Text="{Binding EndPositionVM.StringValue, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" />
            <Label Grid.Row="1" Grid.Column="0" Margin="0 5 0 0" Target="{Binding ElementName=basePositionTextBox}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_BaseOffset}" ToolTip="{x:Static p:dnSpy_Resources.LocalHexSettings_BaseOffset_ToolTip}" />
            <TextBox Grid.Row="1" Grid.Column="1" Margin="5 5 0 0" Name="basePositionTextBox" Text="{Binding BasePositionVM.StringValue, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" />
            <Label Grid.Row="1" Grid.Column="2" Margin="5 5 0 0" Target="{Binding ElementName=offsetBitSizeTextBox}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_OffsetBitSize}" />
            <TextBox Grid.Row="1" Grid.Column="3" Margin="5 5 0 0" Name="offsetBitSizeTextBox" Text="{Binding OffsetBitSizeVM.StringValue, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" />
            <Label Grid.Row="2" Grid.Column="0" Margin="0 5 0 0" Target="{Binding ElementName=bytesPerLineTextBox}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_BytesPerLine}" />
            <TextBox Grid.Row="2" Grid.Column="1" Margin="5 5 0 0" Name="bytesPerLineTextBox" Text="{Binding BytesPerLineVM.StringValue, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" />
            <Label Grid.Row="2" Grid.Column="2" Margin="5 5 0 0" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_ValueFormat}" Target="{Binding ElementName=hexValuesDisplayFormatComboBox}" />
            <ComboBox Grid.Row="2" Grid.Column="3" Margin="5 5 0 0" HorizontalAlignment="Stretch" Name="hexValuesDisplayFormatComboBox" DisplayMemberPath="Name" ItemsSource="{Binding HexValuesDisplayFormatVM.Items}" SelectedIndex="{Binding HexValuesDisplayFormatVM.SelectedIndex}" VerticalContentAlignment="Center" />

            <CheckBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Margin="5 5 0 0" IsChecked="{Binding ShowOffsetColumn}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_ShowOffset}" />
            <CheckBox Grid.Row="3" Grid.Column="2" Grid.ColumnSpan="2" Margin="10 5 0 0" IsChecked="{Binding ShowValuesColumn}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_ShowValues}" />
            <CheckBox Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="2" Margin="5 5 0 0" IsChecked="{Binding ShowAsciiColumn}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_ShowASCII}" />
            <CheckBox Grid.Row="4" Grid.Column="2" Grid.ColumnSpan="2" Margin="10 5 0 0" IsChecked="{Binding UseRelativePositions}" Content="{x:Static p:dnSpy_Resources.LocalHexSettings_UseRelOffsets}" ToolTip="{x:Static p:dnSpy_Resources.LocalHexSettings_UseRelOffsets_ToolTip}" />
        </Grid>

        <Grid Grid.Row="1" Margin="0 5 5 5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="0" Content="{x:Static p:dnSpy_Resources.Button_DefaultSettings}" Style="{StaticResource DialogButton}" Margin="5,0,5,0" Command="{Binding ResetToDefaultCommand}" />
            <Button Grid.Column="2" Content="{x:Static p:dnSpy_Resources.Button_OK}" IsEnabled="{Binding HasError, Converter={StaticResource NegateBooleanConverter}}" IsDefault="True" Style="{StaticResource DialogButton}" Margin="0,0,5,0" Click="okButton_Click" />
            <Button Grid.Column="3" Content="{x:Static p:dnSpy_Resources.Button_Cancel}" IsCancel="True" Style="{StaticResource DialogButton}" Margin="0,0,5,0" />
            <Button Grid.Column="4" Content="{x:Static p:dnSpy_Resources.Button_RestoreSettings}" Style="{StaticResource DialogButton}" Command="{Binding ReinitializeCommand}" />
        </Grid>
    </Grid>
</winlocal:WindowBase>
