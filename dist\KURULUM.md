# DnSpyAI Eklentisi Kurulum Kılavuzu

Bu belge, DnSpyAI eklentisinin dnSpy'a nasıl kurulacağını ve kullanılacağını açıklar.

## Gereksinimler

- dnSpy (en son sürüm)
- .NET Framework 4.7.2 veya üzeri
- OpenAI API anahtarı (eklentinin AI özellikleri için)

## Kurulum Adımları

1. dnSpy'ı kapatın (açıksa).

2. DnSpyAI.dll dosyasını dnSpy'ın Extensions klasörüne kopyalayın:
   - Genellikle `C:\Program Files\dnSpy\Extensions` veya dnSpy'ı kurduğunuz dizindeki `Extensions` klasörü.
   - Eğer Extensions klasörü yoksa, oluşturun.

3. OpenAI API anahtarınızı ayarlayın:
   - Sistem ortam değişkenlerinde `OPENAI_API_KEY` adında bir değişken oluşturun ve değer olarak API anahtarınızı girin.
   - Veya eklenti ilk çalıştırıldığında API anahtarını manuel olarak girebilirsiniz.

4. dnSpy'ı başlatın.

## Kullanım

Eklenti kurulduktan sonra, dnSpy'da aşağıdaki özelliklere erişebilirsiniz:

### AI Paneli

- Menüden "AI Asistan" > "AI Paneli Aç" seçeneğini tıklayın.
- Açılan panelde, AI ile sohbet edebilir, kod hakkında sorular sorabilirsiniz.

### Kod Analizi

1. Kod editöründe analiz etmek istediğiniz kodu seçin.
2. Sağ tıklayın ve "AI ile Analiz Et" seçeneğini tıklayın.
3. AI, seçili kodu analiz edecek ve öneriler sunacaktır.

### Kod Düzenleme

1. Kod editöründe düzenlemek istediğiniz kodu seçin.
2. Sağ tıklayın ve "AI ile Düzenle" seçeneğini tıklayın.
3. Açılan iletişim kutusuna düzenleme talimatlarını girin.
4. AI, talimatlarınıza göre kodu düzenleyecektir.

## Sorun Giderme

Eğer eklenti yüklenmezse veya çalışmazsa:

1. dnSpy'ın sürümünün uyumlu olduğundan emin olun.
2. DnSpyAI.dll dosyasının doğru klasöre kopyalandığından emin olun.
3. API anahtarının doğru ayarlandığından emin olun.
4. dnSpy'ı yönetici olarak çalıştırmayı deneyin.

## Geri Bildirim ve Destek

Sorunlar, öneriler veya geri bildirimler için lütfen GitHub üzerinden iletişime geçin.

---

Not: Bu eklenti, dnSpy'ın MEF (Managed Extensibility Framework) sistemini kullanır ve dnSpy'ın API'sine bağlıdır. dnSpy güncellemeleri, eklentinin çalışmasını etkileyebilir.
