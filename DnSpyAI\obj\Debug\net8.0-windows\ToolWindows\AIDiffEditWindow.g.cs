﻿#pragma checksum "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DF5F056F35B8F596136F6B8105A9F2CE519DE9F8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DnSpyAI.ToolWindows;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Editing;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Rendering;
using ICSharpCode.AvalonEdit.Search;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DnSpyAI.ToolWindows {
    
    
    /// <summary>
    /// AIDiffEditWindow
    /// </summary>
    public partial class AIDiffEditWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 82 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtInstructions;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExecute;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUpdateOriginal;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ICSharpCode.AvalonEdit.TextEditor txtOriginalCode;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal ICSharpCode.AvalonEdit.TextEditor txtModifiedCode;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUpdateOriginalBottom;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCopyOriginal;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCopyModified;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DnSpyAI.x;component/toolwindows/aidiffeditwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtInstructions = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.btnExecute = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            this.btnExecute.Click += new System.Windows.RoutedEventHandler(this.btnExecute_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnUpdateOriginal = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            this.btnUpdateOriginal.Click += new System.Windows.RoutedEventHandler(this.btnUpdateOriginal_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtOriginalCode = ((ICSharpCode.AvalonEdit.TextEditor)(target));
            return;
            case 5:
            
            #line 119 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OriginalLine_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 120 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OriginalAll_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.txtModifiedCode = ((ICSharpCode.AvalonEdit.TextEditor)(target));
            return;
            case 8:
            
            #line 145 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ModifiedLine_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 146 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ModifiedAll_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnUpdateOriginalBottom = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            this.btnUpdateOriginalBottom.Click += new System.Windows.RoutedEventHandler(this.btnUpdateOriginal_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnCopyOriginal = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            this.btnCopyOriginal.Click += new System.Windows.RoutedEventHandler(this.btnCopyOriginal_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnCopyModified = ((System.Windows.Controls.Button)(target));
            
            #line 166 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            this.btnCopyModified.Click += new System.Windows.RoutedEventHandler(this.btnCopyModified_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\ToolWindows\AIDiffEditWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.btnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

