/*
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
*/

using System;
using Iced.Intel;

namespace dnSpy.Disassembly.X86 {
	sealed class GasAppSettingsPage : DisassemblyCodeStyleAppSettingsPage {
		public override double Order => CodeStyleConstants.CODESTYLE_GAS_ORDER;
		public override Guid Guid => new Guid("B2A9B538-925A-4029-9158-8C2FE632764D");
		public override string Title => CodeStyleConstants.GAS_NAME;

		public GasAppSettingsPage(GasDisassemblySettings x86DisassemblySettings)
			: base(x86DisassemblySettings, x86DisassemblySettings.Clone(), new GasFormatter(SymbolResolver.Instance)) { }

		public override void OnApply() =>
			((GasDisassemblySettings)x86DisassemblySettings).CopyTo((GasDisassemblySettings)_global_x86DisassemblySettings);
	}
}
