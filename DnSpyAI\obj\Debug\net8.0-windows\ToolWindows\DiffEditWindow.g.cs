﻿#pragma checksum "..\..\..\..\ToolWindows\DiffEditWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1C97F0D45E866E64202933BB5C8CFD2F5AEB05A0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DnSpyAI.ToolWindows;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DnSpyAI.ToolWindows {
    
    
    /// <summary>
    /// DiffEditWindow
    /// </summary>
    public partial class DiffEditWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtInstructions;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExecute;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUpdateOriginal;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOriginalCode;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtModifiedCode;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCopyOriginal;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCopyModified;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.15.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DnSpyAI.x;component/toolwindows/diffeditwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.15.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtInstructions = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.btnExecute = ((System.Windows.Controls.Button)(target));
            
            #line 31 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            this.btnExecute.Click += new System.Windows.RoutedEventHandler(this.btnExecute_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnUpdateOriginal = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            this.btnUpdateOriginal.Click += new System.Windows.RoutedEventHandler(this.btnUpdateOriginal_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtOriginalCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            
            #line 62 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OriginalLine_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 63 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OriginalAll_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.txtModifiedCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            
            #line 87 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ModifiedLine_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 88 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ModifiedAll_Copy_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnCopyOriginal = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            this.btnCopyOriginal.Click += new System.Windows.RoutedEventHandler(this.btnCopyOriginal_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnCopyModified = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            this.btnCopyModified.Click += new System.Windows.RoutedEventHandler(this.btnCopyModified_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\ToolWindows\DiffEditWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.btnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

