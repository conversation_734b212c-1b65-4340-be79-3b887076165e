=== WriteToILEditorWithTwoPhase BAŞLADI (KESIN ÇÖZÜM) ===
Kod uzunluğu: 1647
Window: MainWindow, DataContext: null
Window: MethodBodyDlg, DataContext: MethodBodyVM
IL editör context bulundu: MethodBodyDlg
[TWO-PHASE] Starting proper two-phase IL writing
[TWO-PHASE] Cleared existing instructions
[FORMAT-CONVERT] System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature() -> instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[FORMAT-CONVERT] System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products() -> instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[FORMAT-CONVERT] System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products() -> instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[FORMAT-CONVERT] System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0 -> instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[FORMAT-CONVERT] System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct) -> instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[FORMAT-CONVERT] System.Void System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>::.ctor(System.Object,System.IntPtr) -> instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[FORMAT-CONVERT] System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0 -> instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[FORMAT-CONVERT] System.Boolean System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>) -> instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
FilterILBodyFromAIResponse: 23 satır bulundu ve dnSpy format'ına çevrildi.
[TWO-PHASE] PHASE 1: Creating instructions
[DNSPY-INSTR] Creating instruction: IL_0000: ldarg.0
[DNSPY-INSTR] Created instruction: ldarg.0
[TWO-PHASE] Mapped IL_0000 -> instruction 0
[DNSPY-INSTR] Creating instruction: IL_0001: brfalse.s IL_003E
[DNSPY-OPERAND] Setting operand: IL_003E
[DNSPY-RESOLVE] Processing: brfalse.s IL_003E
[DNSPY-RESOLVE] Branch target: IL_003E
[DNSPY-OPERAND] WriteValue success: IL_003E
[DNSPY-INSTR] Created instruction: brfalse.s
[TWO-PHASE] Mapped IL_0001 -> instruction 1
[DNSPY-INSTR] Creating instruction: IL_0003: ldarg.0
[DNSPY-INSTR] Created instruction: ldarg.0
[TWO-PHASE] Mapped IL_0003 -> instruction 2
[DNSPY-INSTR] Creating instruction: IL_0004: call instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-OPERAND] Setting operand: instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-RESOLVE] Processing: call instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-MEMBER] Resolving: instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-MEMBER] Method pattern: string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature
[DNSPY-MEMBERREF] Creating MemberRef: string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature
[ASSEMBLY-REF] Looking for assembly: mscorlib
[ASSEMBLY-REF] Failed to create assembly reference - no fallback available
[DNSPY-MEMBERREF] Failed to get assembly reference
[DNSPY-MEMBER] Field pattern: string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-RESOLVE] Fallback to string: instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: call instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[MANUAL-OPERAND] Other set: instance string class Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[DNSPY-INSTR] Created instruction: call
[TWO-PHASE] Mapped IL_0004 -> instruction 3
[DNSPY-INSTR] Creating instruction: IL_0009: brfalse.s IL_003E
[DNSPY-OPERAND] Setting operand: IL_003E
[DNSPY-RESOLVE] Processing: brfalse.s IL_003E
[DNSPY-RESOLVE] Branch target: IL_003E
[DNSPY-OPERAND] WriteValue success: IL_003E
[DNSPY-INSTR] Created instruction: brfalse.s
[TWO-PHASE] Mapped IL_0009 -> instruction 4
[DNSPY-INSTR] Creating instruction: IL_000B: ldarg.0
[DNSPY-INSTR] Created instruction: ldarg.0
[TWO-PHASE] Mapped IL_000B -> instruction 5
[DNSPY-INSTR] Creating instruction: IL_000C: callvirt instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-OPERAND] Setting operand: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-RESOLVE] Processing: callvirt instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-MEMBER] Resolving: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-MEMBER] Method pattern: [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products
[DNSPY-MEMBERREF] Creating MemberRef: [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products
[ASSEMBLY-REF] Looking for assembly: mscorlib
[ASSEMBLY-REF] Failed to create assembly reference - no fallback available
[DNSPY-MEMBERREF] Failed to get assembly reference
[DNSPY-MEMBER] Field pattern: [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-RESOLVE] Fallback to string: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: callvirt instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[MANUAL-OPERAND] Other set: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-INSTR] Created instruction: callvirt
[TWO-PHASE] Mapped IL_000C -> instruction 6
[DNSPY-INSTR] Creating instruction: IL_0011: brfalse.s IL_003E
[DNSPY-OPERAND] Setting operand: IL_003E
[DNSPY-RESOLVE] Processing: brfalse.s IL_003E
[DNSPY-RESOLVE] Branch target: IL_003E
[DNSPY-OPERAND] WriteValue success: IL_003E
[DNSPY-INSTR] Created instruction: brfalse.s
[TWO-PHASE] Mapped IL_0011 -> instruction 7
[DNSPY-INSTR] Creating instruction: IL_0013: ldarg.0
[DNSPY-INSTR] Created instruction: ldarg.0
[TWO-PHASE] Mapped IL_0013 -> instruction 8
[DNSPY-INSTR] Creating instruction: IL_0014: callvirt instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-OPERAND] Setting operand: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-RESOLVE] Processing: callvirt instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-MEMBER] Resolving: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-MEMBER] Method pattern: [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products
[DNSPY-MEMBERREF] Creating MemberRef: [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products
[ASSEMBLY-REF] Looking for assembly: mscorlib
[ASSEMBLY-REF] Failed to create assembly reference - no fallback available
[DNSPY-MEMBERREF] Failed to get assembly reference
[DNSPY-MEMBER] Field pattern: [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-RESOLVE] Fallback to string: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: callvirt instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[MANUAL-OPERAND] Other set: instance [mscorlib]System.Collections.Generic.List`1<class Stimulsoft.Base.Licenses.StiLicenseProduct> class Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[DNSPY-INSTR] Created instruction: callvirt
[TWO-PHASE] Mapped IL_0014 -> instruction 9
[DNSPY-INSTR] Creating instruction: IL_0019: ldsfld instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-OPERAND] Setting operand: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-RESOLVE] Processing: ldsfld instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-MEMBER] Resolving: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-MEMBER] Field pattern: [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-RESOLVE] Fallback to string: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: ldsfld instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[MANUAL-OPERAND] Other set: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-INSTR] Created instruction: ldsfld
[TWO-PHASE] Mapped IL_0019 -> instruction 10
[DNSPY-INSTR] Creating instruction: IL_001E: dup
[DNSPY-INSTR] Created instruction: dup
[TWO-PHASE] Mapped IL_001E -> instruction 11
[DNSPY-INSTR] Creating instruction: IL_001F: brtrue.s IL_0038
[DNSPY-OPERAND] Setting operand: IL_0038
[DNSPY-RESOLVE] Processing: brtrue.s IL_0038
[DNSPY-RESOLVE] Branch target: IL_0038
[DNSPY-OPERAND] WriteValue success: IL_0038
[DNSPY-INSTR] Created instruction: brtrue.s
[TWO-PHASE] Mapped IL_001F -> instruction 12
[DNSPY-INSTR] Creating instruction: IL_0021: pop
[DNSPY-INSTR] Created instruction: pop
[TWO-PHASE] Mapped IL_0021 -> instruction 13
[DNSPY-INSTR] Creating instruction: IL_0022: ldsfld Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-OPERAND] Setting operand: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-RESOLVE] Processing: ldsfld Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-MEMBER] Resolving: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-MEMBER] Field pattern: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-MEMBER] Found field: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-RESOLVE] Member resolved via dnSpy system: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-OPERAND] WriteValue success: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[DNSPY-INSTR] Created instruction: ldsfld
[TWO-PHASE] Mapped IL_0022 -> instruction 14
[DNSPY-INSTR] Creating instruction: IL_0027: ldftn instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-OPERAND] Setting operand: instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-RESOLVE] Processing: ldftn instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-MEMBER] Resolving: instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-MEMBER] Method pattern: bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0
[DNSPY-MEMBERREF] Creating MemberRef: bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0
[ASSEMBLY-REF] Looking for assembly: mscorlib
[ASSEMBLY-REF] Failed to create assembly reference - no fallback available
[DNSPY-MEMBERREF] Failed to get assembly reference
[DNSPY-MEMBER] Field pattern: bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-RESOLVE] Fallback to string: instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: ldftn instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[MANUAL-OPERAND] Other set: instance bool class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[DNSPY-INSTR] Created instruction: ldftn
[TWO-PHASE] Mapped IL_0027 -> instruction 15
[DNSPY-INSTR] Creating instruction: IL_002D: newobj instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-OPERAND] Setting operand: instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-RESOLVE] Processing: newobj instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-MEMBER] Resolving: instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-MEMBER] Method pattern: void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor
[DNSPY-MEMBERREF] Creating MemberRef: void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor
[ASSEMBLY-REF] Looking for assembly: mscorlib
[ASSEMBLY-REF] Failed to create assembly reference - no fallback available
[DNSPY-MEMBERREF] Failed to get assembly reference
[DNSPY-MEMBER] Field pattern: void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-RESOLVE] Fallback to string: instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: newobj instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[MANUAL-OPERAND] Other set: instance void [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, class bool>::.ctor(object,native int)
[DNSPY-INSTR] Created instruction: newobj
[TWO-PHASE] Mapped IL_002D -> instruction 16
[DNSPY-INSTR] Creating instruction: IL_0032: dup
[DNSPY-INSTR] Created instruction: dup
[TWO-PHASE] Mapped IL_0032 -> instruction 17
[DNSPY-INSTR] Creating instruction: IL_0033: stsfld instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-OPERAND] Setting operand: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-RESOLVE] Processing: stsfld instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-MEMBER] Resolving: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-MEMBER] Field pattern: [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-RESOLVE] Fallback to string: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: stsfld instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[MANUAL-OPERAND] Other set: instance [mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool> class Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[DNSPY-INSTR] Created instruction: stsfld
[TWO-PHASE] Mapped IL_0033 -> instruction 18
[DNSPY-INSTR] Creating instruction: IL_0038: call instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-OPERAND] Setting operand: instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-RESOLVE] Processing: call instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-MEMBER] Resolving: instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-MEMBER] Method pattern: bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>
[DNSPY-MEMBERREF] Creating MemberRef: bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>
[ASSEMBLY-REF] Looking for assembly: System.Core
[ASSEMBLY-REF] Failed to create assembly reference - no fallback available
[DNSPY-MEMBERREF] Failed to get assembly reference
[DNSPY-MEMBER] Field pattern: bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-RESOLVE] Fallback to string: instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation.
[DNSPY-OPERAND] Using manual fallback
[MANUAL-OPERAND] Setting manually: call instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[MANUAL-OPERAND] Other set: instance bool class [System.Core]System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>([mscorlib]System.Collections.Generic.IEnumerable`1<class Stimulsoft.Base.Licenses.StiLicenseProduct>,[mscorlib]System.Func`2<class Stimulsoft.Base.Licenses.StiLicenseProduct, bool>)
[DNSPY-INSTR] Created instruction: call
[TWO-PHASE] Mapped IL_0038 -> instruction 19
[DNSPY-INSTR] Creating instruction: IL_003D: ret
[DNSPY-INSTR] Created instruction: ret
[TWO-PHASE] Mapped IL_003D -> instruction 20
[DNSPY-INSTR] Creating instruction: IL_003E: ldc.i4.1
[DNSPY-INSTR] Created instruction: ldc.i4.1
[TWO-PHASE] Mapped IL_003E -> instruction 21
[DNSPY-INSTR] Creating instruction: IL_003F: ret
[DNSPY-INSTR] Created instruction: ret
[TWO-PHASE] Mapped IL_003F -> instruction 22
[TWO-PHASE] PHASE 2: Resolving branch targets
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: BranchTarget
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: Method
[BRANCH-RESOLVE] Checking operand type: BranchTarget
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: Method
[BRANCH-RESOLVE] Checking operand type: BranchTarget
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: Method
[BRANCH-RESOLVE] Checking operand type: Field
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: BranchTarget
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: Field
[BRANCH-RESOLVE] Checking operand type: Method
[BRANCH-RESOLVE] Checking operand type: Method
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: Field
[BRANCH-RESOLVE] Checking operand type: Method
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: None
[BRANCH-RESOLVE] Checking operand type: None
[TWO-PHASE] PHASE 3: Adding instructions to collection
[TWO-PHASE] Successfully added 23 instructions
WriteToILEditorWithProperTwoPhase sonucu: True
