=== WriteToILEditorWithTwoPhase BAŞLADI ===
Kod uzunluğu: 1646
Window: MainWindow, DataContext: null
Window: MethodBodyDlg, DataContext: MethodBodyVM
IL editör context bulundu: MethodBodyDlg
=== SIMPLIFIED IL EDITOR WRITING ===
Found InstructionsListVM: IndexObservableCollection`1
Cleared existing IL instructions
FilterILBodyFromAIResponse: 23 satır bulundu.
Filtered 23 IL instruction lines
[SIMPLE] InstructionVM from: IL_0000: ldarg.0
[SIMPLE] InstructionVM from: IL_0001: brfalse.s IL_003E
[SIMPLE-OPERAND] Processing: IL_003E
[RESOLVE-ADV] Processing: brfalse.s IL_003E
[RESOLVE-ADV] Branch target: IL_003E
[SIMPLE-OPERAND] WriteValue success: IL_003E
[SIMPLE] InstructionVM from: IL_0003: ldarg.0
[SIMPLE] InstructionVM from: IL_0004: call System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[SIMPLE-OPERAND] Processing: System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[RESOLVE-ADV] Processing: call System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[GENERIC-RESOLVE] Resolving method: System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[GENERIC-PARSE] Type: Stimulsoft.Base.Licenses.StiLicenseKey, Method: get_Signature, GenericArgs: 
[METHOD-RESOLVE] Resolving method: Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature
[METHOD-FOUND] Method found: System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[RESOLVE-ADV] Method resolved: System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[SIMPLE-OPERAND] WriteValue success: System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()
[SIMPLE] InstructionVM from: IL_0009: brfalse.s IL_003E
[SIMPLE-OPERAND] Processing: IL_003E
[RESOLVE-ADV] Processing: brfalse.s IL_003E
[RESOLVE-ADV] Branch target: IL_003E
[SIMPLE-OPERAND] WriteValue success: IL_003E
[SIMPLE] InstructionVM from: IL_000B: ldarg.0
[SIMPLE] InstructionVM from: IL_000C: callvirt System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[SIMPLE-OPERAND] Processing: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[RESOLVE-ADV] Processing: callvirt System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[GENERIC-RESOLVE] Resolving method: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[GENERIC-PARSE] Type: Stimulsoft.Base.Licenses.StiLicenseKey, Method: get_Products, GenericArgs: 
[METHOD-RESOLVE] Resolving method: Stimulsoft.Base.Licenses.StiLicenseKey::get_Products
[METHOD-FOUND] Method found: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[RESOLVE-ADV] Method resolved: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[SIMPLE-OPERAND] WriteValue success: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[SIMPLE] InstructionVM from: IL_0011: brfalse.s IL_003E
[SIMPLE-OPERAND] Processing: IL_003E
[RESOLVE-ADV] Processing: brfalse.s IL_003E
[RESOLVE-ADV] Branch target: IL_003E
[SIMPLE-OPERAND] WriteValue success: IL_003E
[SIMPLE] InstructionVM from: IL_0013: ldarg.0
[SIMPLE] InstructionVM from: IL_0014: callvirt System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[SIMPLE-OPERAND] Processing: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[RESOLVE-ADV] Processing: callvirt System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[GENERIC-RESOLVE] Resolving method: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[GENERIC-PARSE] Type: Stimulsoft.Base.Licenses.StiLicenseKey, Method: get_Products, GenericArgs: 
[METHOD-RESOLVE] Resolving method: Stimulsoft.Base.Licenses.StiLicenseKey::get_Products
[METHOD-FOUND] Method found: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[RESOLVE-ADV] Method resolved: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[SIMPLE-OPERAND] WriteValue success: System.Collections.Generic.List`1<Stimulsoft.Base.Licenses.StiLicenseProduct> Stimulsoft.Base.Licenses.StiLicenseKey::get_Products()
[SIMPLE] InstructionVM from: IL_0019: ldsfld System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[SIMPLE-OPERAND] Processing: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[RESOLVE-ADV] Processing: ldsfld System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[RESOLVE-ADV] Field resolved: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[SIMPLE-OPERAND] WriteValue success: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[SIMPLE] InstructionVM from: IL_001E: dup
[SIMPLE] InstructionVM from: IL_001F: brtrue.s IL_0038
[SIMPLE-OPERAND] Processing: IL_0038
[RESOLVE-ADV] Processing: brtrue.s IL_0038
[RESOLVE-ADV] Branch target: IL_0038
[SIMPLE-OPERAND] WriteValue success: IL_0038
[SIMPLE] InstructionVM from: IL_0021: pop
[SIMPLE] InstructionVM from: IL_0022: ldsfld Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[SIMPLE-OPERAND] Processing: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[RESOLVE-ADV] Processing: ldsfld Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[RESOLVE-ADV] Field resolved: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[SIMPLE-OPERAND] WriteValue success: Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9
[SIMPLE] InstructionVM from: IL_0027: ldftn System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[SIMPLE-OPERAND] Processing: System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[RESOLVE-ADV] Processing: ldftn System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[GENERIC-RESOLVE] Resolving method: System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[RESOLVE-ADV] Method resolved: System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[SIMPLE-OPERAND] WriteValue success: System.Boolean Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<IsValidOnWebFramework>b__0_0(Stimulsoft.Base.Licenses.StiLicenseProduct)
[SIMPLE] InstructionVM from: IL_002D: newobj System.Void System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>::.ctor(System.Object,System.IntPtr)
[SIMPLE-OPERAND] Processing: System.Void System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>::.ctor(System.Object,System.IntPtr)
[RESOLVE-ADV] Processing: newobj System.Void System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>::.ctor(System.Object,System.IntPtr)
[GENERIC-RESOLVE] Resolving method: System.Void System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>::.ctor(System.Object,System.IntPtr)
[GENERIC-PARSE] Type: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>, Method: .ctor, GenericArgs: 
[CTOR-RESOLVE] Resolving constructor for type: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>
[CTOR-GENERIC] Detected generic type with inline args: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>
[CTOR-GENERIC] Base type: System.Func`2, Inline args: Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean
[GENERIC-TYPEREF] Creating instantiated TypeRef for: System.Func`2 with args: Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean
[TYPEREF] Creating TypeRef for: System.Func`2
[TYPEREF] Successfully imported System type: System.Func`2
[GENERIC-PARSE] Parsing 2 generic arguments: Stimulsoft.Base.Licenses.StiLicenseProduct, System.Boolean
[TYPESIG] Creating TypeSig for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Creating TypeRef for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Created TypeRef with 3-param constructor for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPESIG] Created ClassTypeSig for: Stimulsoft.Base.Licenses.StiLicenseProduct
[GENERIC-PARSE] Added type: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPESIG] Creating TypeSig for: System.Boolean
[TYPEREF] Creating TypeRef for: System.Boolean
[TYPEREF] Successfully imported System type: System.Boolean
[TYPESIG] Created ClassTypeSig for: System.Boolean
[GENERIC-PARSE] Added type: System.Boolean
[GENERIC-TYPEREF] ClassTypeSig type not found
[MEMBERREF-CTOR] Creating MemberRef for constructor: System.Func`2::.ctor(System.Object,System.IntPtr)
[CTORSIG] Creating constructor MethodSig for parameters: System.Object,System.IntPtr
[TYPESIG] Creating TypeSig for: System.Object
[TYPEREF] Creating TypeRef for: System.Object
[TYPEREF] Successfully imported System type: System.Object
[TYPESIG] Created ClassTypeSig for: System.Object
[TYPESIG] Creating TypeSig for: System.IntPtr
[TYPEREF] Creating TypeRef for: System.IntPtr
[TYPEREF] Successfully imported System type: System.IntPtr
[TYPESIG] Created ClassTypeSig for: System.IntPtr
[RESOLVE-ADV] Fallback to string: System.Void System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>::.ctor(System.Object,System.IntPtr)
[SIMPLE-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation., using fallback
[SIMPLE] InstructionVM from: IL_0032: dup
[SIMPLE] InstructionVM from: IL_0033: stsfld System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[SIMPLE-OPERAND] Processing: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[RESOLVE-ADV] Processing: stsfld System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[RESOLVE-ADV] Field resolved: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[SIMPLE-OPERAND] WriteValue success: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0
[SIMPLE] InstructionVM from: IL_0038: call System.Boolean System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[SIMPLE-OPERAND] Processing: System.Boolean System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[RESOLVE-ADV] Processing: call System.Boolean System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[GENERIC-RESOLVE] Resolving method: System.Boolean System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[GENERIC-PARSE] Type: System.Linq.Enumerable, Method: Any, GenericArgs: Stimulsoft.Base.Licenses.StiLicenseProduct
[METHOD-RESOLVE] Resolving method: System.Linq.Enumerable::Any
[METHOD-GENERIC] Creating MethodSpec for generic method: Any<Stimulsoft.Base.Licenses.StiLicenseProduct>
[METHOD-GENERIC] Trying external generic method
[MEMBERREF-GENERIC] Creating MemberRef for generic method: System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[TYPEREF] Creating TypeRef for: System.Linq.Enumerable
[TYPEREF] Successfully imported System type: System.Linq.Enumerable
[MEMBERREF] Creating MemberRef for: System.Linq.Enumerable::Any(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[TYPEREF] Creating TypeRef for: System.Linq.Enumerable
[TYPEREF] Successfully imported System type: System.Linq.Enumerable
[METHODSIG] Creating MethodSig for parameters: System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>
[TYPESIG] Creating TypeSig for: System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>
[GENERIC-TYPESIG] Processing: System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>
[GENERIC-TYPESIG] Base: System.Collections.Generic.IEnumerable`1, Count: 1, Args: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Creating TypeRef for: System.Collections.Generic.IEnumerable`1
[TYPEREF] Successfully imported System type: System.Collections.Generic.IEnumerable`1
[GENERIC-PARSE] Parsing 1 generic arguments: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPESIG] Creating TypeSig for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Creating TypeRef for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Created TypeRef with 3-param constructor for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPESIG] Created ClassTypeSig for: Stimulsoft.Base.Licenses.StiLicenseProduct
[GENERIC-PARSE] Added type: Stimulsoft.Base.Licenses.StiLicenseProduct
[GENERIC-TYPESIG] Successfully created GenericInstSig for: System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct> with 1 args
[TYPESIG] Created GenericTypeSig for: System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>
[TYPESIG] Creating TypeSig for: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>
[GENERIC-TYPESIG] Processing: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>
[GENERIC-TYPESIG] Base: System.Func`2, Count: 2, Args: Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean
[TYPEREF] Creating TypeRef for: System.Func`2
[TYPEREF] Successfully imported System type: System.Func`2
[GENERIC-PARSE] Parsing 2 generic arguments: Stimulsoft.Base.Licenses.StiLicenseProduct, System.Boolean
[TYPESIG] Creating TypeSig for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Creating TypeRef for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPEREF] Created TypeRef with 3-param constructor for: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPESIG] Created ClassTypeSig for: Stimulsoft.Base.Licenses.StiLicenseProduct
[GENERIC-PARSE] Added type: Stimulsoft.Base.Licenses.StiLicenseProduct
[TYPESIG] Creating TypeSig for: System.Boolean
[TYPEREF] Creating TypeRef for: System.Boolean
[TYPEREF] Successfully imported System type: System.Boolean
[TYPESIG] Created ClassTypeSig for: System.Boolean
[GENERIC-PARSE] Added type: System.Boolean
[GENERIC-TYPESIG] Successfully created GenericInstSig for: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> with 2 args
[TYPESIG] Created GenericTypeSig for: System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>
[RESOLVE-ADV] Fallback to string: System.Boolean System.Linq.Enumerable::Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(System.Collections.Generic.IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean>)
[SIMPLE-OPERAND] WriteValue failed: Exception has been thrown by the target of an invocation., using fallback
[SIMPLE] InstructionVM from: IL_003D: ret
[SIMPLE] InstructionVM from: IL_003E: ldc.i4.1  // Değişiklik burada
[SIMPLE] InstructionVM from: IL_003F: ret
Phase 1: Created 23 instructions
ResolveBranchTargets başlatıldı
Branch target çözüldü: brfalse.s -> IL_003E
Branch target çözüldü: brfalse.s -> IL_003E
Branch target çözüldü: brfalse.s -> IL_003E
Branch target çözüldü: brtrue.s -> IL_0038
Successfully added 23 IL instructions
WriteToILEditorSimplified sonucu: True
