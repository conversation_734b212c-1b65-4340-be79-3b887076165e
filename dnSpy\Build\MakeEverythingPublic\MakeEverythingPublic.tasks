<Project>
	<UsingTask AssemblyFile="$(MSBuildThisFileDirectory)..\compiled\MakeEverythingPublic.dll" TaskName="MakeEverythingPublic.MakeEverythingPublic" />
	<Target Name="MakeEverythingPublic" AfterTargets="ResolveAssemblyReferences">
		<MakeEverythingPublic AssembliesToMakePublic="$(MakeEverythingPublicAssemblies)" ReferencePath="@(ReferencePath)" DestinationDirectory="$(IntermediateOutputPath)MakeEverythingPublic" IVTString="$(MakeEverythingPublicIVTString)">
			<Output ItemName="_MakeEverythingPublic_OutputReferencePath" TaskParameter="OutputReferencePath" />
		</MakeEverythingPublic>
		<ItemGroup>
			<ReferencePath Remove="@(ReferencePath)" />
			<ReferencePath Include="@(_MakeEverythingPublic_OutputReferencePath)" />
		</ItemGroup>
	</Target>
	<!-- https://github.com/dotnet/project-system/blob/master/docs/design-time-builds.md#targets-that-run-during-design-time-builds -->
	<Target Name="MakeEverythingPublicDesignTime" AfterTargets="ResolveAssemblyReferencesDesignTime">
		<MakeEverythingPublic AssembliesToMakePublic="$(MakeEverythingPublicAssemblies)" ReferencePath="@(ReferencePath)" DestinationDirectory="$(IntermediateOutputPath)MakeEverythingPublic" IVTString="$(MakeEverythingPublicIVTString)">
			<Output ItemName="_MakeEverythingPublic_OutputReferencePathDesignTime" TaskParameter="OutputReferencePath" />
		</MakeEverythingPublic>
		<ItemGroup>
			<ReferencePath Remove="@(ReferencePath)" />
			<ReferencePath Include="@(_MakeEverythingPublic_OutputReferencePathDesignTime)" />
		</ItemGroup>
	</Target>
</Project>
