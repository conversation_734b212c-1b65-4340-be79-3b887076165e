<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<UserControl x:Class="dnSpy.Language.Intellisense.SignatureHelpPresenterControl"
             x:ClassModifier="internal"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Focusable="False">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <Style TargetType="{x:Type ButtonBase}" x:Key="UpDownButtonStyle">
            <Setter Property="Width" Value="16" />
            <Setter Property="Height" Value="16" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Focusable" Value="False" />
            <Setter Property="FocusVisualStyle" Value="{x:Null}" />
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderBrush" Value="Transparent" />
            <Setter Property="Foreground" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="HorizontalContentAlignment" Value="Center" />
            <Setter Property="VerticalContentAlignment" Value="Center" />
            <Setter Property="Padding" Value="1" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ButtonBase}">
                        <Border
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            SnapsToDevicePixels="True">
                            <ContentPresenter
                                RecognizesAccessKey="True"
                                Margin="{TemplateBinding Padding}"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                Focusable="False">
                            </ContentPresenter>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <Grid>
        <!-- High DPI fix, see ToolTip's style for more info -->
        <Border BorderThickness="0"
                SnapsToDevicePixels="True"
                Background="{DynamicResource SignatureHelpBackground}"/>
        <Border BorderThickness="1"
                SnapsToDevicePixels="True"
                Background="{DynamicResource SignatureHelpBackground}"
                TextBlock.Foreground="{DynamicResource SignatureHelpForeground}"
                BorderBrush="{DynamicResource SignatureHelpBorder}">
            <Grid Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0" Margin="2" Visibility="{Binding HasMoreThanOneSignature, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Button Grid.Column="0" Style="{StaticResource UpDownButtonStyle}" Command="{Binding SelectPreviousSignatureCommand}">
                        <Button.Content>
                            <Path Fill="{DynamicResource SignatureHelpForeground}"
                                  Data="M0,9L9,9 4.5,0 0,9z"
                                  SnapsToDevicePixels="True" />
                        </Button.Content>
                    </Button>
                    <ContentPresenter Grid.Column="1" VerticalAlignment="Center" Margin="2 0" Content="{Binding SignatureCountObject}" />
                    <Button Grid.Column="2" Style="{StaticResource UpDownButtonStyle}" Command="{Binding SelectNextSignatureCommand}">
                        <Button.Content>
                            <Path Fill="{DynamicResource SignatureHelpForeground}"
                                  Data="M0,0L9,0 4.5,9 0,0z"
                                  SnapsToDevicePixels="True" />
                        </Button.Content>
                    </Button>
                </Grid>
                <Grid Grid.Column="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <ContentPresenter Grid.Row="0" Content="{Binding SignatureObject}" />
                    <ContentPresenter Grid.Row="1" Content="{Binding SignatureDocumentationObject}" Visibility="{Binding HasSignatureDocumentationObject, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <Grid Grid.Row="2" Visibility="{Binding HasParameter, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <ContentPresenter Grid.Column="0" Margin="0 0 5 0" Content="{Binding ParameterNameObject}" />
                        <ContentPresenter Grid.Column="1" Margin="0 0 0 0" Content="{Binding ParameterDocumentationObject}" />
                    </Grid>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
