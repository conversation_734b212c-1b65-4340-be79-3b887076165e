<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\DnSpyCommon.props" />

  <PropertyGroup>
    <!-- It should only use netstandard. netxxx/netcorappx.x are added here so dotnet build
         with a '-f netxxx/netcorappx.x' arg can be used without failing the build -->
    <TargetFrameworks>netstandard2.0;$(TargetFrameworks)</TargetFrameworks>
    <Copyright>$(DnSpyAssemblyCopyright)</Copyright>
    <Version>$(DnSpyAssemblyVersion)</Version>
    <InformationalVersion>$(DnSpyAssemblyInformationalVersion)</InformationalVersion>

    <SignAssembly>True</SignAssembly>
    <AssemblyOriginatorKeyFile>..\..\dnSpy.snk</AssemblyOriginatorKeyFile>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="dnlib" Version="$(DnlibVersion)" />
    <PackageReference Include="Microsoft.Build.Framework" Version="$(MSBuildNuGetVersion)" />
    <PackageReference Include="Microsoft.Build.Utilities.Core" Version="$(MSBuildNuGetVersion)" />
  </ItemGroup>

</Project>
