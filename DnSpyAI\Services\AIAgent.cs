using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using DnSpyAI.Services;
using DnSpyAI.Services.AI;
using dnlib.DotNet.Emit;

namespace DnSpyAI.Services
{
    public class AIAgent
    {
        private static AIAgent? instance;
        private readonly AIService aiService;
        private readonly DnSpyNavigationService navigationService;

        public static AIAgent Instance => instance ??= new AIAgent();

        private AIAgent()
        {
            aiService = AIService.Instance;
            navigationService = DnSpyNavigationService.Instance;
        }

        public string QuickTest()
        {
            try
            {
                var code = GetCurrentCode();
                var result = $"Kod okuma testi: {(string.IsNullOrEmpty(code) ? "BAŞARISIZ" : "BAŞARILI")}";
                
                if (!string.IsNullOrEmpty(code))
                {
                    result += $"\nKod uzunluğu: {code.Length} karakter";
                    result += $"\nİlk 50 karakter: {code.Substring(0, Math.Min(50, code.Length))}...";
                }
                
                return result;
            }
            catch (Exception ex)
            {
                return $"Test hatası: {ex.Message}";
            }
        }

        public string GetCurrentCode()
        {
            try
            {
                // Gerçek kod okuma
                var realCode = GetRealCodeFromEditVM();
                if (!string.IsNullOrEmpty(realCode) && realCode != "main.cs")
                {
                    return realCode;
                }

                // Clipboard fallback
                if (Clipboard.ContainsText())
                {
                    var text = Clipboard.GetText();
                    if (!string.IsNullOrEmpty(text) && IsCodeLike(text))
                    {
                        return text;
                    }
                }

                // Simple fallback
                return GetSimpleFallback();
            }
            catch (Exception ex)
            {
                LoggingService.Error($"GetCurrentCode failed: {ex.Message}", ex);
                return "main.cs";
            }
        }        private string GetRealCodeFromEditVM()
        {
            try
            {
                // DEBUG: Tüm window'ları logla
                var debugPath = @"C:\Users\<USER>\Desktop\dnspyai-son-4\aiagent-windows-debug.log";
                var debugInfo = new System.Text.StringBuilder();
                
                debugInfo.AppendLine("=== ALL WINDOWS DEBUG (STRICT PRIORITY) ===");
                debugInfo.AppendLine($"Total windows: {Application.Current.Windows.Count}");
                
                // STEP 1: First priority - Look for IL editors (Method Body dialogs) - STRICT CHECK
                debugInfo.AppendLine("=== STEP 1: IL EDITORS CHECK ===");
                foreach (Window window in Application.Current.Windows)
                {
                    var windowTypeName = window.GetType().Name;
                    var windowTitle = window.Title ?? "";
                    var dataContext = window.DataContext;
                    
                    debugInfo.AppendLine($"Window: {windowTypeName} - Title: '{windowTitle}' - DataContext: {dataContext?.GetType().Name ?? "null"}");
                    
                    // IL Method Body Dialog - HIGHEST PRIORITY
                    if (windowTypeName == "MethodBodyDlg" || windowTypeName.Contains("MethodBody") ||
                        windowTypeName.Contains("EditIL") || windowTypeName.Contains("ILEdit") || 
                        window.Title.Contains("Edit IL") || window.Title.Contains("IL Code") ||
                        window.Title.Contains("Method Body") || window.Title.Contains("CIL"))
                    {
                        if (dataContext != null)
                        {
                            DebugHelper.LogMessage($"Found IL Editor (PRIORITY 1): {windowTypeName}, DataContext: {dataContext.GetType().Name}");
                            debugInfo.AppendLine($"*** PROCESSING IL EDITOR: {windowTypeName} ***");
                            var code = ExtractCodeFromILOnly(dataContext);
                            if (!string.IsNullOrEmpty(code))
                            {
                                debugInfo.AppendLine($"*** STRICT RETURN: IL CODE FROM {windowTypeName} ***");
                                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                return code; // STRICT EXIT - No further processing
                            }
                            debugInfo.AppendLine($"*** IL EDITOR {windowTypeName} HAD NO CODE ***");
                        }
                    }
                }
                
                // STEP 2: Second priority - Look for C# editors (EditCodeDlg) - STRICT CHECK
                debugInfo.AppendLine("=== STEP 2: C# EDITORS CHECK ===");
                foreach (Window window in Application.Current.Windows)
                {
                    var windowTypeName = window.GetType().Name;
                    var dataContext = window.DataContext;
                    
                    // C# Code Dialog - SECOND PRIORITY
                    if (windowTypeName == "EditCodeDlg")
                    {
                        if (dataContext?.GetType().Name.Contains("EditMethodCodeVM") == true ||
                            dataContext?.GetType().Name.Contains("EditClassVM") == true)
                        {
                            DebugHelper.LogMessage($"Found C# Editor (PRIORITY 2): {windowTypeName}, DataContext: {dataContext.GetType().Name}");
                            debugInfo.AppendLine($"*** PROCESSING C# EDITOR: {windowTypeName} ***");
                            var code = ExtractCodeFromCSharpOnly(dataContext);
                            if (!string.IsNullOrEmpty(code))
                            {
                                debugInfo.AppendLine($"*** STRICT RETURN: C# CODE FROM {windowTypeName} ***");
                                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                return code; // STRICT EXIT - No further processing
                            }
                            debugInfo.AppendLine($"*** C# EDITOR {windowTypeName} HAD NO CODE ***");
                        }
                    }
                }
                
                // STEP 3: Last resort - Generic edit dialogs - ONLY IF NO PRIORITY FOUND
                debugInfo.AppendLine("=== STEP 3: GENERIC EDITORS CHECK ===");
                foreach (Window window in Application.Current.Windows)
                {
                    var windowTypeName = window.GetType().Name;
                    var dataContext = window.DataContext;
                    
                    // Generic Edit Dialog - LAST PRIORITY
                    if (windowTypeName.Contains("Edit") && window.Title.Contains("Edit"))
                    {
                        if (dataContext != null)
                        {
                            DebugHelper.LogMessage($"Found Generic Edit Dialog (PRIORITY 3): {windowTypeName}, DataContext: {dataContext.GetType().Name}");
                            debugInfo.AppendLine($"*** PROCESSING GENERIC EDITOR: {windowTypeName} ***");
                            var code = ExtractCodeFromVM(dataContext);
                            if (!string.IsNullOrEmpty(code) && code != "main.cs")
                            {
                                debugInfo.AppendLine($"*** GENERIC RETURN: CODE FROM {windowTypeName} ***");
                                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                return code; // Return here too
                            }
                            debugInfo.AppendLine($"*** GENERIC EDITOR {windowTypeName} HAD NO CODE ***");
                        }
                    }
                }
                
                debugInfo.AppendLine("=== NO CODE FOUND IN ANY PRIORITY LEVEL ===");
                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                return "";
            }
            catch (Exception ex)
            {
                LoggingService.Error($"GetRealCodeFromEditVM failed: {ex.Message}", ex);
                return "";
            }
        }private string ExtractCodeFromVM(object vm)
        {
            try
            {
                // Debug bilgisi hem konsola hem dosyaya yaz
                var debugPath = @"C:\Users\<USER>\Desktop\dnspyai-son-4\aiagent-debug.log";
                var debugInfo = new System.Text.StringBuilder();
                
                debugInfo.AppendLine("=== EXTRACT CODE DEBUG START ===");
                debugInfo.AppendLine($"VM Type: {vm.GetType().FullName}");
                
                var properties = vm.GetType().GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                debugInfo.AppendLine($"Total properties found: {properties.Length}");
                
                foreach (var prop in properties)
                {
                    try
                    {
                        debugInfo.AppendLine($"Checking property: {prop.Name} ({prop.PropertyType.Name})");
                        
                        if (prop.PropertyType == typeof(string))
                        {
                            var value = prop.GetValue(vm) as string;
                            debugInfo.AppendLine($"String property {prop.Name}: Length={value?.Length ?? 0}");
                            
                            if (!string.IsNullOrEmpty(value))
                            {
                                var preview = value.Length > 100 ? value.Substring(0, 100) + "..." : value;
                                debugInfo.AppendLine($"Property {prop.Name} content: {preview}");
                                
                                if (IsCodeLike(value))
                                {
                                    debugInfo.AppendLine($"*** FOUND CODE in property: {prop.Name} ***");
                                    System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                    return value;
                                }
                            }
                        }                        else if (!prop.PropertyType.IsPrimitive && prop.PropertyType != typeof(string))
                        {
                            var nestedValue = prop.GetValue(vm);
                            if (nestedValue != null)
                            {
                                debugInfo.AppendLine($"Checking nested object in {prop.Name}: {nestedValue.GetType().Name}");
                                  // Special handling for Documents and SelectedDocument
                                if (prop.Name == "Documents" || prop.Name == "SelectedDocument")
                                {
                                    var codeResult = ExtractCodeFromDocuments(nestedValue, debugInfo, $"{prop.Name}");
                                    if (!string.IsNullOrEmpty(codeResult))
                                    {
                                        debugInfo.AppendLine($"*** FOUND CODE in {prop.Name} ***");
                                        System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                        return codeResult;
                                    }
                                }
                                  // Special handling for IL Method Body VMs - CilBodyVM is the key!
                                if (prop.Name == "CilBodyVM" || prop.Name == "NativeMethodBodyVM" || prop.Name.Contains("BodyVM"))
                                {
                                    debugInfo.AppendLine($"*** INVESTIGATING IL BODY VM: {prop.Name} ***");
                                    var ilBodyCode = ExtractCodeFromILBodyVM(nestedValue, debugInfo, prop.Name);
                                    if (!string.IsNullOrEmpty(ilBodyCode))
                                    {
                                        debugInfo.AppendLine($"*** FOUND IL CODE in {prop.Name} ***");
                                        System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                        return ilBodyCode;
                                    }
                                }
                                
                                var nestedProps = nestedValue.GetType().GetProperties();
                                foreach (var nestedProp in nestedProps.Where(p => p.PropertyType == typeof(string)))
                                {
                                    try
                                    {
                                        var nestedText = nestedProp.GetValue(nestedValue) as string;
                                        if (!string.IsNullOrEmpty(nestedText) && nestedText.Length > 50)
                                        {
                                            debugInfo.AppendLine($"Nested string in {prop.Name}.{nestedProp.Name}: Length={nestedText.Length}");
                                            
                                            if (IsCodeLike(nestedText))
                                            {
                                                debugInfo.AppendLine($"*** FOUND CODE in nested property: {prop.Name}.{nestedProp.Name} ***");
                                                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                                return nestedText;
                                            }
                                        }
                                    }
                                    catch { }
                                }
                            }
                        }
                    }
                    catch (Exception propEx)
                    {
                        debugInfo.AppendLine($"Error checking property {prop.Name}: {propEx.Message}");
                    }
                }
                  debugInfo.AppendLine("=== NO CODE FOUND IN VM ===");
                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                return "";
            }
            catch (Exception ex)
            {
                var debugPath = @"C:\Users\<USER>\Desktop\dnspyai-son-4\aiagent-debug.log";
                System.IO.File.WriteAllText(debugPath, $"ExtractCodeFromVM failed: {ex.Message}\n{ex.StackTrace}");
                return "";
            }
        }

        private string ExtractCodeFromILOnly(object dataContext)
        {
            try
            {
                var debugInfo = new System.Text.StringBuilder();
                debugInfo.AppendLine("=== IL ONLY EXTRACTION ===");
                debugInfo.AppendLine($"DataContext Type: {dataContext.GetType().FullName}");
                
                var properties = dataContext.GetType().GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                
                // SADECE IL-related property'leri ara
                foreach (var prop in properties)
                {
                    try
                    {
                        // IL Body VM'leri ara
                        if (prop.Name == "CilBodyVM" || prop.Name == "NativeMethodBodyVM" || prop.Name.Contains("BodyVM"))
                        {
                            var nestedValue = prop.GetValue(dataContext);
                            if (nestedValue != null)
                            {
                                debugInfo.AppendLine($"*** FOUND IL BODY VM: {prop.Name} ***");
                                var ilCode = ExtractCodeFromILBodyVM(nestedValue, debugInfo, prop.Name);
                                if (!string.IsNullOrEmpty(ilCode))
                                {
                                    return ilCode;
                                }
                            }
                        }
                        
                        // IL Instructions koleksiyonlarını direkt ara
                        else if (prop.Name.Contains("Instruction") || prop.Name.Contains("Local") || 
                                prop.Name.Contains("IL") || prop.Name.Contains("Cil"))
                        {
                            var value = prop.GetValue(dataContext);
                            if (value != null)
                            {
                                debugInfo.AppendLine($"*** CHECKING IL PROPERTY: {prop.Name} ***");
                                if (value is string strValue && IsCodeLike(strValue))
                                {
                                    return strValue;
                                }
                                else if (value is System.Collections.IEnumerable enumerable && !(value is string))
                                {
                                    var collectionText = ExtractTextFromCollection(value, debugInfo, prop.Name);
                                    if (!string.IsNullOrEmpty(collectionText))
                                    {
                                        return collectionText;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        debugInfo.AppendLine($"Error in IL extraction for {prop.Name}: {ex.Message}");
                    }
                }
                
                debugInfo.AppendLine("=== NO IL CODE FOUND ===");
                return "";
            }
            catch (Exception ex)
            {
                LoggingService.Error($"ExtractCodeFromILOnly failed: {ex.Message}", ex);
                return "";
            }
        }

        private string ExtractCodeFromCSharpOnly(object dataContext)
        {
            try
            {
                var debugInfo = new System.Text.StringBuilder();
                debugInfo.AppendLine("=== C# ONLY EXTRACTION ===");
                debugInfo.AppendLine($"DataContext Type: {dataContext.GetType().FullName}");
                
                var properties = dataContext.GetType().GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                
                // SADECE C#/Documents-related property'leri ara
                foreach (var prop in properties)
                {
                    try
                    {
                        // Documents property'leri ara (C# code için)
                        if (prop.Name == "Documents" || prop.Name == "SelectedDocument")
                        {
                            var nestedValue = prop.GetValue(dataContext);
                            if (nestedValue != null)
                            {
                                debugInfo.AppendLine($"*** FOUND DOCUMENTS PROPERTY: {prop.Name} ***");
                                var codeResult = ExtractCodeFromDocuments(nestedValue, debugInfo, prop.Name);
                                if (!string.IsNullOrEmpty(codeResult))
                                {
                                    return codeResult;
                                }
                            }
                        }
                        
                        // String property'leri sadece C# code pattern'i için kontrol et
                        else if (prop.PropertyType == typeof(string))
                        {
                            var value = prop.GetValue(dataContext) as string;
                            if (!string.IsNullOrEmpty(value) && IsCodeLike(value) && !IsILCode(value))
                            {
                                debugInfo.AppendLine($"*** FOUND C# CODE in property: {prop.Name} ***");
                                return value;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        debugInfo.AppendLine($"Error in C# extraction for {prop.Name}: {ex.Message}");
                    }
                }
                
                debugInfo.AppendLine("=== NO C# CODE FOUND ===");
                return "";
            }
            catch (Exception ex)
            {
                LoggingService.Error($"ExtractCodeFromCSharpOnly failed: {ex.Message}", ex);
                return "";
            }
        }

        private bool IsILCode(string code)
        {
            // IL kod pattern'lerini kontrol et
            if (string.IsNullOrEmpty(code)) return false;
            
            var lowerCode = code.ToLower();
            
            // IL opcode pattern'leri
            return lowerCode.Contains("il_") || 
                   lowerCode.Contains("ldarg") || 
                   lowerCode.Contains("ldloc") || 
                   lowerCode.Contains("stloc") || 
                   lowerCode.Contains("ldstr") || 
                   lowerCode.Contains("call ") || 
                   lowerCode.Contains("ret") ||
                   lowerCode.Contains("dnspy.asmedi") ||
                   code.Contains("InstructionVM");
        }

        private string ExtractCodeFromDocuments(object documentsObj, System.Text.StringBuilder debugInfo, string parentName)
        {
            try
            {
                debugInfo.AppendLine($"=== DEEP DIVE INTO {parentName} ===");
                debugInfo.AppendLine($"{parentName} Type: {documentsObj.GetType().FullName}");
                
                // If it's a collection, iterate through items
                if (documentsObj is System.Collections.IEnumerable enumerable && !(documentsObj is string))
                {
                    debugInfo.AppendLine($"{parentName} is a collection, iterating...");
                    int index = 0;
                    foreach (var item in enumerable)
                    {
                        if (item != null)
                        {
                            debugInfo.AppendLine($"Collection item[{index}]: {item.GetType().Name}");
                            var code = ExtractCodeFromSingleDocument(item, debugInfo, $"{parentName}[{index}]");
                            if (!string.IsNullOrEmpty(code))
                            {
                                return code;
                            }
                            index++;
                        }
                    }
                }
                else
                {
                    // Single document
                    return ExtractCodeFromSingleDocument(documentsObj, debugInfo, parentName);
                }
                
                return "";
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractCodeFromDocuments error: {ex.Message}");
                return "";
            }
        }

        private string ExtractCodeFromSingleDocument(object docObj, System.Text.StringBuilder debugInfo, string docName)
        {
            try
            {
                debugInfo.AppendLine($"=== ANALYZING DOCUMENT {docName} ===");
                debugInfo.AppendLine($"Document Type: {docObj.GetType().FullName}");
                
                var docProps = docObj.GetType().GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                debugInfo.AppendLine($"Document properties count: {docProps.Length}");
                
                // Common code properties in document objects
                var codePropertyNames = new[] { 
                    "Text", "Code", "Content", "Body", "Source", "Data", 
                    "CodeText", "SourceCode", "TextContent", "Buffer", "Document"
                };
                
                foreach (var prop in docProps)
                {
                    try
                    {
                        debugInfo.AppendLine($"Document property: {prop.Name} ({prop.PropertyType.Name})");
                          if (prop.PropertyType == typeof(string))
                        {
                            var value = prop.GetValue(docObj) as string;
                            debugInfo.AppendLine($"String property {prop.Name}: Length={value?.Length ?? 0}");
                            

                            if (!string.IsNullOrEmpty(value) && value.Length > 20)
                            {
                                var preview = value.Length > 100 ? value.Substring(0, 100) + "..." : value;
                                debugInfo.AppendLine($"Property {prop.Name} content: {preview}");


                                if (IsCodeLike(value))
                                {
                                    debugInfo.AppendLine($"*** FOUND CODE in document property: {docName}.{prop.Name} ***");
                                    return value;
                                }
                            }
                        }
                        else if (prop.Name == "TextView" || prop.Name == "TextViewHost")
                        {
                            // Special handling for TextView objects
                            var textViewObj = prop.GetValue(docObj);
                            if (textViewObj != null)
                            {
                                debugInfo.AppendLine($"Found TextView object: {prop.Name}, type: {textViewObj.GetType().Name}");
                                var textFromView = ExtractTextFromTextView(textViewObj, debugInfo, $"{docName}.{prop.Name}");
                                if (!string.IsNullOrEmpty(textFromView))
                                {
                                    debugInfo.AppendLine($"*** FOUND CODE in TextView: {docName}.{prop.Name} ***");
                                    return textFromView;
                                }
                            }
                        }
                        else if (codePropertyNames.Contains(prop.Name, StringComparer.OrdinalIgnoreCase))
                        {
                            var nestedValue = prop.GetValue(docObj);
                            if (nestedValue != null)
                            {
                                debugInfo.AppendLine($"Found potential code property: {prop.Name}, type: {nestedValue.GetType().Name}");
                                

                                // Try to extract text from nested objects
                                var nestedCode = ExtractTextFromObject(nestedValue, debugInfo, $"{docName}.{prop.Name}");
                                if (!string.IsNullOrEmpty(nestedCode))
                                {
                                    return nestedCode;
                                }
                            }
                        }
                    }
                    catch (Exception propEx)
                    {
                        debugInfo.AppendLine($"Error checking document property {prop.Name}: {propEx.Message}");
                    }
                }
                
                return "";
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractCodeFromSingleDocument error: {ex.Message}");
                return "";
            }
        }

        private string ExtractTextFromObject(object obj, System.Text.StringBuilder debugInfo, string objName)
        {
            try
            {
                debugInfo.AppendLine($"=== EXTRACTING TEXT FROM {objName} ===");
                debugInfo.AppendLine($"Object Type: {obj.GetType().FullName}");
                
                if (obj is string str)
                {
                    return str;
                }
                
                var props = obj.GetType().GetProperties();
                foreach (var prop in props.Where(p => p.PropertyType == typeof(string)))
                {
                    try
                    {
                        var value = prop.GetValue(obj) as string;
                        if (!string.IsNullOrEmpty(value) && value.Length > 20)
                        {
                            debugInfo.AppendLine($"Text property {prop.Name}: Length={value.Length}");
                            var preview = value.Length > 100 ? value.Substring(0, 100) + "..." : value;
                            debugInfo.AppendLine($"Content: {preview}");
                            

                            if (IsCodeLike(value))
                            {
                                debugInfo.AppendLine($"*** FOUND CODE in text property: {objName}.{prop.Name} ***");
                                return value;
                            }
                        }
                    }
                    catch { }
                }
                
                return "";
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractTextFromObject error: {ex.Message}");
                return "";
            }
        }        private string ExtractCodeFromILBodyVM(object ilBodyVM, System.Text.StringBuilder debugInfo, string vmName)
        {
            try
            {
                debugInfo.AppendLine($"=== EXTRACTING FROM IL BODY VM: {vmName} ===");
                debugInfo.AppendLine($"IL Body VM Type: {ilBodyVM.GetType().FullName}");
                
                var vmType = ilBodyVM.GetType();
                var properties = vmType.GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                
                // ÖNCELİK: IL Instructions koleksiyonlarını ara
                var instructionPropertyNames = new[] { 
                    "InstructionsListVM", "Instructions", "InstructionList", "InstructionCollection",
                    "LocalsListVM", "Locals", "LocalsList", "LocalCollection"
                };
                
                foreach (var prop in properties)
                {
                    try
                    {
                        debugInfo.AppendLine($"IL Body property: {prop.Name} ({prop.PropertyType.Name})");
                        
                        // ÖNCELİKLE Instructions koleksiyonunu ara
                        if (instructionPropertyNames.Contains(prop.Name, StringComparer.OrdinalIgnoreCase))
                        {
                            var collection = prop.GetValue(ilBodyVM);
                            if (collection != null)
                            {
                                debugInfo.AppendLine($"*** FOUND IL INSTRUCTIONS COLLECTION: {prop.Name} ***");
                                var collectionText = ExtractTextFromCollection(collection, debugInfo, $"{vmName}.{prop.Name}");
                                if (!string.IsNullOrEmpty(collectionText))
                                {
                                    debugInfo.AppendLine($"*** FOUND IL CODE in instructions {vmName}.{prop.Name} ***");
                                    return collectionText;
                                }
                            }
                        }
                        // String property'leri kontrol et
                        else if (prop.PropertyType == typeof(string))
                        {
                            var value = prop.GetValue(ilBodyVM) as string;
                            if (!string.IsNullOrEmpty(value) && value.Length > 20)
                            {
                                debugInfo.AppendLine($"String property {prop.Name}: Length={value.Length}");
                                var preview = value.Length > 100 ? value.Substring(0, 100) + "..." : value;
                                debugInfo.AppendLine($"Content: {preview}");


                                if (IsCodeLike(value))
                                {
                                    debugInfo.AppendLine($"*** FOUND IL CODE in {vmName}.{prop.Name} ***");
                                    return value;
                                }
                            }
                        }
                        // Collection property'leri kontrol et (Instructions gibi)
                        else if (prop.PropertyType.IsGenericType || prop.PropertyType.IsArray || 
                                prop.Name.Contains("Instruction") || prop.Name.Contains("List"))
                        {
                            var collection = prop.GetValue(ilBodyVM);
                            if (collection != null && !prop.Name.Contains("Command")) // Command'ları skip et
                            {
                                debugInfo.AppendLine($"Found collection property: {prop.Name}, type: {collection.GetType().Name}");
                                var collectionText = ExtractTextFromCollection(collection, debugInfo, $"{vmName}.{prop.Name}");
                                if (!string.IsNullOrEmpty(collectionText))
                                {
                                    return collectionText;
                                }
                            }
                        }
                        // Nested object'leri kontrol et (ama Command'ları skip et)
                        else if (!prop.PropertyType.IsPrimitive && prop.PropertyType != typeof(string) && 
                                !prop.Name.Contains("Command"))
                        {
                            var nestedValue = prop.GetValue(ilBodyVM);
                            if (nestedValue != null)
                            {
                                debugInfo.AppendLine($"Nested object in {prop.Name}: {nestedValue.GetType().Name}");
                                

                                // Recursive extraction
                                var nestedCode = ExtractTextFromObject(nestedValue, debugInfo, $"{vmName}.{prop.Name}");
                                if (!string.IsNullOrEmpty(nestedCode))
                                {
                                    return nestedCode;
                                }
                            }
                        }
                    }
                    catch (Exception propEx)
                    {
                        debugInfo.AppendLine($"Error checking IL Body property {prop.Name}: {propEx.Message}");
                    }
                }
                
                debugInfo.AppendLine($"=== NO IL CODE FOUND IN {vmName} ===");
                return "";
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractCodeFromILBodyVM error: {ex.Message}");
                return "";
            }
        }        private string ExtractTextFromCollection(object collection, System.Text.StringBuilder debugInfo, string collectionName)
        {
            try
            {
                debugInfo.AppendLine($"=== EXTRACTING FROM COLLECTION: {collectionName} ===");
                
                if (collection is System.Collections.IEnumerable enumerable)
                {
                    var instructions = new System.Text.StringBuilder();
                    int count = 0;
                    
                    foreach (var item in enumerable)
                    {
                        if (item != null)
                        {
                            // ÖNEMLİ FİX: InstructionVM için özel işlem yap
                            string itemText = "";
                              if (item.GetType().Name == "InstructionVM" || item.GetType().FullName?.Contains("InstructionVM") == true)
                            {
                                // IL Instruction'dan gerçek opcode ve operand bilgilerini çıkar
                                itemText = ExtractILInstructionDetails(item, debugInfo, count);
                            }
                            else
                            {
                                // Normal ToString() kullan
                                itemText = item.ToString();
                            }
                            

                            if (!string.IsNullOrEmpty(itemText) && itemText != "dnSpy.AsmEditor.MethodBody.InstructionVM")
                            {
                                instructions.AppendLine(itemText);
                                count++;
                                if (count > 1000) break; // Too many instructions
                            }
                        }
                    }
                    
                    var fullText = instructions.ToString();
                    debugInfo.AppendLine($"Collection {collectionName}: {count} items, {fullText.Length} chars");
                    
                    if (!string.IsNullOrEmpty(fullText) && fullText.Length > 50)
                    {
                        var preview = fullText.Length > 200 ? fullText.Substring(0, 200) + "..." : fullText;
                        debugInfo.AppendLine($"Collection content: {preview}");
                        
                        if (IsCodeLike(fullText))
                        {
                            debugInfo.AppendLine($"*** FOUND IL CODE in collection {collectionName} ***");
                            return fullText;
                        }
                    }
                }
                
                return "";
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractTextFromCollection error: {ex.Message}");
                return "";
            }
        }

        private string ExtractILInstructionDetails(object instructionVM, System.Text.StringBuilder debugInfo, int index)
        {
            try
            {
                debugInfo.AppendLine($"=== EXTRACTING IL INSTRUCTION DETAILS [{index}] ===");
                debugInfo.AppendLine($"InstructionVM Type: {instructionVM.GetType().FullName}");
                
                var instructionType = instructionVM.GetType();
                var result = new System.Text.StringBuilder();
                
                // 1. Offset bilgisini al
                string offset = "IL_0000";
                try
                {
                    var offsetProp = instructionType.GetProperty("Offset");
                    if (offsetProp != null)
                    {
                        var offsetValue = offsetProp.GetValue(instructionVM);
                        if (offsetValue != null)
                        {
                            offset = $"IL_{Convert.ToUInt32(offsetValue):X4}";
                        }
                    }
                }
                catch (Exception ex)
                {
                    debugInfo.AppendLine($"Error getting offset: {ex.Message}");
                }
                
                // 2. Code (OpCode) bilgisini al
                string opcode = "nop";
                try
                {
                    var codeProp = instructionType.GetProperty("Code");
                    if (codeProp != null)
                    {
                        var codeValue = codeProp.GetValue(instructionVM);
                        if (codeValue != null)
                        {
                            opcode = codeValue.ToString().ToLowerInvariant();
                            // Code enum değerini IL opcode'una dönüştür
                            opcode = ConvertCodeToILOpcode(opcode);
                        }
                    }
                }
                catch (Exception ex)
                {
                    debugInfo.AppendLine($"Error getting code: {ex.Message}");
                }
                
                // 3. Operand bilgisini al
                string operand = "";
                try
                {
                    var operandVMProp = instructionType.GetProperty("InstructionOperandVM");
                    if (operandVMProp != null)
                    {
                        var operandVM = operandVMProp.GetValue(instructionVM);
                        if (operandVM != null)
                        {
                            operand = ExtractOperandDetails(operandVM, debugInfo);
                        }
                    }
                }
                catch (Exception ex)
                {
                    debugInfo.AppendLine($"Error getting operand: {ex.Message}");
                }
                
                // 4. Tam IL instruction'ı oluştur
                result.Append(offset);
                result.Append(": ");
                result.Append(opcode);
                
                if (!string.IsNullOrEmpty(operand))
                {
                    result.Append(" ");
                    result.Append(operand);
                }
                
                var fullInstruction = result.ToString();
                debugInfo.AppendLine($"Extracted IL instruction: {fullInstruction}");
                
                return fullInstruction;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractILInstructionDetails error: {ex.Message}");
                return $"IL_0000: nop // Error extracting instruction";
            }
        }

        private string ConvertCodeToILOpcode(string code)
        {
            // dnlib Code enum'ını IL opcode'larına map et
            var mappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                {"ret", "ret"},
                {"nop", "nop"},
                {"ldloc_0", "ldloc.0"},
                {"ldloc_1", "ldloc.1"},
                {"ldloc_2", "ldloc.2"},
                {"ldloc_3", "ldloc.3"},
                {"stloc_0", "stloc.0"},
                {"stloc_1", "stloc.1"},
                {"stloc_2", "stloc.2"},
                {"stloc_3", "stloc.3"},
                {"ldarg_0", "ldarg.0"},
                {"ldarg_1", "ldarg.1"},
                {"ldarg_2", "ldarg.2"},
                {"ldarg_3", "ldarg.3"},
                {"ldc_i4_0", "ldc.i4.0"},
                {"ldc_i4_1", "ldc.i4.1"},
                {"ldc_i4_m1", "ldc.i4.m1"},
                {"br_s", "br.s"},
                {"brfalse_s", "brfalse.s"},
                {"brtrue_s", "brtrue.s"},
                {"beq_s", "beq.s"},
                {"bne_un_s", "bne.un.s"},
                {"blt_s", "blt.s"},
                {"ble_s", "ble.s"},
                {"bgt_s", "bgt.s"},
                {"bge_s", "bge.s"},
                {"call", "call"},
                {"callvirt", "callvirt"},
                {"newobj", "newobj"},
                {"ldstr", "ldstr"},
                {"ldnull", "ldnull"},
                {"dup", "dup"},
                {"pop", "pop"},
                {"add", "add"},
                {"sub", "sub"},
                {"mul", "mul"},
                {"div", "div"},
                {"ldsfld", "ldsfld"},
                {"ldfld", "ldfld"},
                {"stsfld", "stsfld"},
                {"stfld", "stfld"},
                {"ldftn", "ldftn"},
                {"ldarg", "ldarg"},
                {"ldloc", "ldloc"},
                {"stloc", "stloc"}
            };
            
            return mappings.ContainsKey(code) ? mappings[code] : code.Replace("_", ".");
        }

        private string ExtractOperandDetails(object operandVM, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine($"=== EXTRACTING OPERAND DETAILS ===");
                debugInfo.AppendLine($"OperandVM Type: {operandVM.GetType().FullName}");
                
                var operandType = operandVM.GetType();
                
                // 1. InstructionOperandType'ı kontrol et
                var operandTypeProp = operandType.GetProperty("InstructionOperandType");
                if (operandTypeProp != null)
                {
                    var operandTypeValue = operandTypeProp.GetValue(operandVM);
                    debugInfo.AppendLine($"Operand type: {operandTypeValue}");
                    
                    // 2. Value property'sinden değeri al
                    var valueProp = operandType.GetProperty("Value");
                    if (valueProp != null)
                    {
                        var value = valueProp.GetValue(operandVM);
                        if (value != null)
                        {
                            debugInfo.AppendLine($"Operand value: {value} (Type: {value.GetType().Name})");
                            
                            // Branch target için özel işlem
                            if (value.GetType().Name == "InstructionVM" || value.GetType().FullName?.Contains("InstructionVM") == true)
                            {
                                // Branch target instruction'ın offset'ini al
                                var targetOffsetProp = value.GetType().GetProperty("Offset");
                                if (targetOffsetProp != null)
                                {
                                    var targetOffset = targetOffsetProp.GetValue(value);
                                    if (targetOffset != null)
                                    {
                                        var targetOffsetStr = $"IL_{Convert.ToUInt32(targetOffset):X4}";
                                        debugInfo.AppendLine($"Branch target: {targetOffsetStr}");
                                        return targetOffsetStr;
                                    }
                                }
                                
                                // Fallback: IL_0000
                                debugInfo.AppendLine("Branch target fallback: IL_0000");
                                return "IL_0000";
                            }
                            
                            // Diğer operand türleri için
                            return FormatOperandValue(value, operandTypeValue?.ToString(), debugInfo);
                        }
                    }
                }
                
                return "";
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractOperandDetails error: {ex.Message}");
                return "";
            }
        }

        private string FormatOperandValue(object value, string operandType, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine($"=== FORMATTING OPERAND VALUE ===");
                debugInfo.AppendLine($"Value: {value}, Type: {value?.GetType().Name}, OperandType: {operandType}");
                
                if (value == null) return "";
                
                switch (operandType?.ToLowerInvariant())
                {
                    case "string":
                        return $"\"{value}\"";
                        
                    case "int32":
                    case "sbyte":
                    case "byte":
                        return value.ToString();
                        
                    case "int64":
                        return value.ToString();
                        
                    case "single":
                    case "double":
                        return value.ToString();
                        
                    case "field":
                    case "method":
                    case "type":
                    case "token":
                        // Method/Field/Type reference için toString kullan
                        var str = value.ToString();
                        debugInfo.AppendLine($"Reference operand: {str}");
                        return str;
                        
                    case "branchtarget":
                        // Bu case yukarıda handle edilmeli
                        debugInfo.AppendLine("BranchTarget case - should be handled above");
                        return "IL_0000";
                        
                    default:
                        var defaultStr = value.ToString();
                        debugInfo.AppendLine($"Default operand format: {defaultStr}");
                        return defaultStr;
                }
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"FormatOperandValue error: {ex.Message}");
                return value?.ToString() ?? "";
            }
        }

        private string GetSimpleFallback()
        {
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "EditCodeDlg" && window.Title.Contains("Edit Code"))
                {
                    return "main.cs"; // 7 karakter - çalışan test
                }
            }
            return "";
        }

        public string DebugGetCurrentCode()
        {
            try
            {
                var code = GetCurrentCode();
                return string.IsNullOrEmpty(code) 
                    ? "❌ Kod okunamadı." 
                    : $"✅ Kod okundu! ({code.Length} karakter)\n\nÖrnek:\n{code.Substring(0, Math.Min(200, code.Length))}...";
            }
            catch (Exception ex)
            {
                return $"❌ Hata: {ex.Message}";
            }
        }

        public async Task<string> TestAIAgent()
        {
            try
            {
                var codeTest = DebugGetCurrentCode();
                var aiTest = await TestAIConnection();
                return $"🔍 Kod Okuma Testi:\n{codeTest}\n\n🤖 AI Bağlantı Testi:\n{aiTest}";
            }
            catch (Exception ex)
            {
                return $"❌ Test hatası: {ex.Message}";
            }
        }

        private async Task<string> TestAIConnection()
        {
            try
            {
                if (aiService?.ActiveProvider == null)
                    return "❌ AI sağlayıcısı bulunamadı";

                var response = await aiService.ActiveProvider.GenerateCodeAsync("Test mesajı: 'Merhaba' kelimesini döndür.");
                
                return string.IsNullOrEmpty(response) 
                    ? "❌ AI yanıt vermedi" 
                    : $"✅ AI bağlantısı başarılı! Yanıt: {response.Substring(0, Math.Min(100, response.Length))}...";
            }
            catch (Exception ex)
            {
                return $"❌ AI bağlantı hatası: {ex.Message}";
            }
        }        // WRITING FUNCTIONALITY - Minimal addition to complement existing reading
        public async Task<bool> WriteCodeToEditor(string newCode)
        {
            try
            {
                var debugPath = @"C:\Users\<USER>\Desktop\dnspyai-son-4\aiagent-write-debug.log";
                var debugInfo = new System.Text.StringBuilder();
                
                debugInfo.AppendLine("=== ENHANCED WRITE CODE DEBUG START ===");
                debugInfo.AppendLine($"New code length: {newCode?.Length ?? 0}");
                
                // Use same algorithm as reading: ExtractCodeFromVM approach
                foreach (Window window in Application.Current.Windows)
                {
                    var windowTypeName = window.GetType().Name;
                    var dataContext = window.DataContext;
                    
                    debugInfo.AppendLine($"Checking window: {windowTypeName}, DataContext: {dataContext?.GetType().Name ?? "null"}");                    // IL Method Body Dialog - PRIORITY 1
                    if (windowTypeName == "MethodBodyDlg" || windowTypeName.Contains("MethodBody") ||
                        windowTypeName.Contains("EditIL") || windowTypeName.Contains("ILEdit") ||
                        window.Title.Contains("Edit IL") || window.Title.Contains("Method Body"))
                    {
                        if (dataContext != null)
                        {
                            debugInfo.AppendLine($"*** FOUND IL EDITOR: {windowTypeName} ***");
                            // ENHANCED DEBUGGING: Log before calling Two Phase method
                            System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                            
                            try
                            {
                                DebugHelper.LogMessage("About to call WriteToILEditorWithTwoPhase...");
                                await WriteToILEditorWithTwoPhase(newCode);
                                DebugHelper.LogMessage("WriteToILEditorWithTwoPhase completed successfully");
                                return true;
                            }
                            catch (Exception twoPhaseEx)
                            {
                                DebugHelper.LogMessage($"WriteToILEditorWithTwoPhase FAILED: {twoPhaseEx.Message}");
                                debugInfo.AppendLine($"*** TWO PHASE ERROR: {twoPhaseEx.Message} ***");
                                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                return false;
                            }
                        }
                    }
                    
                    // C# Code Dialog - PRIORITY 2
                    else if (windowTypeName == "EditCodeDlg")
                    {
                        if (dataContext?.GetType().Name.Contains("EditMethodCodeVM") == true)
                        {
                            debugInfo.AppendLine($"*** FOUND C# EDITOR: {windowTypeName} ***");
                            var result = WriteToCSharpEditor(dataContext, newCode, debugInfo);
                            if (result)
                            {
                                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                                return true;
                            }
                        }
                    }
                }
                
                debugInfo.AppendLine("*** NO COMPATIBLE EDITOR FOUND ***");
                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.Error("WriteCodeToEditor failed", ex);
                return false;
            }
        }

        private bool WriteToILEditor(object dataContext, string newCode, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine("=== WRITING TO IL EDITOR ===");
                
                // Find CilBodyVM -> InstructionsListVM path
                var cilBodyVM = GetProperty(dataContext, "CilBodyVM");
                if (cilBodyVM == null)
                {
                    debugInfo.AppendLine("CilBodyVM not found");
                    return false;
                }
                
                var instructionsListVM = GetProperty(cilBodyVM, "InstructionsListVM");
                if (instructionsListVM == null)
                {
                    debugInfo.AppendLine("InstructionsListVM not found");
                    return false;
                }
                
                debugInfo.AppendLine($"Found InstructionsListVM: {instructionsListVM.GetType().Name}");
                
                // Clear existing instructions
                var clearMethod = instructionsListVM.GetType().GetMethod("Clear");
                if (clearMethod != null)
                {
                    clearMethod.Invoke(instructionsListVM, null);
                    debugInfo.AppendLine("Cleared existing IL instructions");
                }
                
                // Parse new IL code and add instructions
                var instructionLines = FilterILBodyFromAIResponse(newCode, debugInfo);
                var addMethod = instructionsListVM.GetType().GetMethod("Add");
                
                if (addMethod != null)
                {
                    int addedCount = 0;
                    foreach (var line in instructionLines.Take(50)) // Limit for safety
                    {
                        var instructionVM = CreateSimpleInstructionVM(line.Trim(), debugInfo);
                        if (instructionVM != null)
                        {
                            addMethod.Invoke(instructionsListVM, new[] { instructionVM });
                            addedCount++;
                        }
                    }
                    
                    debugInfo.AppendLine($"Successfully added {addedCount} IL instructions");
                    return addedCount > 0;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"WriteToILEditor error: {ex.Message}");
                return false;
            }
        }

        private bool WriteToILEditorSimplified(object dataContext, string newCode, StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine("=== SIMPLIFIED IL EDITOR WRITING ===");
                
                // Get the instruction collection
                var cilBodyVM = GetProperty(dataContext, "CilBodyVM");
                if (cilBodyVM == null)
                {
                    debugInfo.AppendLine("CilBodyVM not found");
                    return false;
                }
                
                var instructionsListVM = GetProperty(cilBodyVM, "InstructionsListVM");
                if (instructionsListVM == null)
                {
                    debugInfo.AppendLine("InstructionsListVM not found");
                    return false;
                }
                
                debugInfo.AppendLine($"Found InstructionsListVM: {instructionsListVM.GetType().Name}");
                
                // Clear existing instructions
                var clearMethod = instructionsListVM.GetType().GetMethod("Clear");
                if (clearMethod == null)
                {
                    debugInfo.AppendLine("Clear method not found");
                    return false;
                }
                
                clearMethod.Invoke(instructionsListVM, null);
                debugInfo.AppendLine("Cleared existing IL instructions");
                
                // Filter and parse IL instructions
                var instructionLines = FilterILBodyFromAIResponse(newCode, debugInfo);
                debugInfo.AppendLine($"Filtered {instructionLines.Count()} IL instruction lines");
                
                var addMethod = instructionsListVM.GetType().GetMethod("Add");
                if (addMethod == null)
                {
                    debugInfo.AppendLine("Add method not found");
                    return false;
                }
                
                // Phase 1: Create all instructions with NULL for complex operands
                var createdVMs = new List<object>();
                var lineToVmMap = new Dictionary<string, object>();
                
                foreach (var line in instructionLines.Take(50)) // Safety limit
                {
                    var instructionVM = CreateInstructionVMSimplified(line.Trim(), debugInfo);
                    if (instructionVM != null)
                    {
                        addMethod.Invoke(instructionsListVM, new[] { instructionVM });
                        createdVMs.Add(instructionVM);
                        // Map label to VM for branch target resolution
                        (string offset, string _, string __) = ParseILLineComponents(line);
                        if (!string.IsNullOrEmpty(offset))
                        {
                            lineToVmMap[offset] = instructionVM;
                        }
                    }
                }
                
                debugInfo.AppendLine($"Phase 1: Created {createdVMs.Count} instructions");
                
                // Phase 2: Resolve branch targets (if any)
                ResolveBranchTargets(instructionLines, createdVMs, lineToVmMap, debugInfo);
                
                debugInfo.AppendLine($"Successfully added {createdVMs.Count} IL instructions");
                return createdVMs.Count > 0;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"WriteToILEditorSimplified error: {ex.Message}");
                return false;
            }
        }

        private bool WriteToCSharpEditor(object dataContext, string newCode, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine("=== WRITING TO C# EDITOR ===");
                
                // Find Documents collection
                var documentsProperty = dataContext.GetType().GetProperty("Documents");
                if (documentsProperty == null)
                {
                    debugInfo.AppendLine("Documents property not found");
                    return false;
                }
                
                var documents = documentsProperty.GetValue(dataContext);
                if (documents is System.Collections.IEnumerable enumerable)
                {
                    foreach (var doc in enumerable)
                    {
                        if (doc != null)
                        {
                            var textViewProperty = doc.GetType().GetProperty("TextView");
                            if (textViewProperty != null)
                            {
                                var textView = textViewProperty.GetValue(doc);
                                if (textView != null)
                                {
                                    return WriteToTextView(textView, newCode, debugInfo);
                                }
                            }
                        }
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"WriteToCSharpEditor error: {ex.Message}");
                return false;
            }
        }

        private bool WriteToTextView(object textView, string newCode, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine("=== WRITING TO TEXTVIEW ===");
                
                var textBufferProp = textView.GetType().GetProperty("TextBuffer");
                if (textBufferProp == null) return false;
                
                var textBuffer = textBufferProp.GetValue(textView);
                if (textBuffer == null) return false;
                
                // Get current text length for full replacement
                var textSnapshotProp = textView.GetType().GetProperty("TextSnapshot");
                int length = 0;
                if (textSnapshotProp != null)
                {
                    var textSnapshot = textSnapshotProp.GetValue(textView);
                    if (textSnapshot != null)
                    {
                        var lengthProp = textSnapshot.GetType().GetProperty("Length");
                        if (lengthProp != null)
                        {
                            length = (int)(lengthProp.GetValue(textSnapshot) ?? 0);
                        }
                    }
                }
                
                // Create edit and replace all text
                var createEditMethod = textBuffer.GetType().GetMethod("CreateEdit", Type.EmptyTypes);
                if (createEditMethod == null) return false;
                
                var edit = createEditMethod.Invoke(textBuffer, null);
                if (edit == null) return false;
                
                try
                {
                    var replaceMethod = edit.GetType().GetMethod("Replace", 
                        new Type[] { typeof(int), typeof(int), typeof(string) });
                    
                    if (replaceMethod == null) return false;
                    
                    replaceMethod.Invoke(edit, new object[] { 0, length, newCode });
                    
                    var applyMethod = edit.GetType().GetMethod("Apply");
                    if (applyMethod == null) return false;
                    
                    applyMethod.Invoke(edit, null);
                    debugInfo.AppendLine("*** TEXT SUCCESSFULLY WRITTEN ***");
                    return true;
                }
                finally
                {
                    if (edit is IDisposable disposable)
                    {
                        disposable.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"WriteToTextView error: {ex.Message}");
                return false;
            }
        }        private object CreateSimpleInstructionVM(string instructionText, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine($"Creating InstructionVM from: {instructionText}");
                (string offset, string opcodeName, string operandText) = ParseILLineComponents(instructionText);
                if (string.IsNullOrEmpty(opcodeName))
                {
                    debugInfo.AppendLine($"Parse failed: {instructionText}");
                    return null;
                }

                // Tam assembly adı ile InstructionVM aransın
                var instructionVMType = FindType("dnSpy.AsmEditor.MethodBody.InstructionVM, dnSpy.AsmEditor");
                if (instructionVMType == null)
                {
                    debugInfo.AppendLine("InstructionVM tipi bulunamadı");
                    return null;
                }
                var instructionVM = Activator.CreateInstance(instructionVMType);
                if (instructionVM == null) return null;

                // OpCode'u bul
                OpCode opCode = MapOpcodeToOpCode(opcodeName);
                // dnSpy'nin Code enum'unu bul
                var codeEnumType = FindType("dnlib.DotNet.Emit.Code");
                object codeEnumValue = null;
                if (codeEnumType != null)
                {
                    codeEnumValue = Enum.Parse(codeEnumType, opCode.Code.ToString());
                    SetProperty(instructionVM, "Code", codeEnumValue);
                }

                // Offset'i ata (varsa)
                if (!string.IsNullOrEmpty(offset))
                {
                    uint offsetVal = 0;
                    if (offset.StartsWith("IL_") && uint.TryParse(offset.Substring(3), System.Globalization.NumberStyles.HexNumber, null, out offsetVal))
                        SetProperty(instructionVM, "Offset", offsetVal);
                }

                // CRITICAL FIX: Operand atama - WriteValue kullan
                var operandVM = GetProperty(instructionVM, "InstructionOperandVM");
                if (operandVM != null && !string.IsNullOrEmpty(operandText))
                {
                    debugInfo.AppendLine($"[OPERAND-FIX] Processing operand: {operandText} for opcode: {opcodeName}");

                    // ÖNCE WriteValue ile operand type'ını otomatik set et
                    var writeValueMethod = operandVM.GetType().GetMethod("WriteValue");
                    if (writeValueMethod != null)
                    {
                        // Operand'ı çözümle
                        var resolvedOperand = ResolveOperandAdvanced(opcodeName, operandText, debugInfo);

                        try
                        {
                            // WriteValue ile operand type otomatik set edilir
                            writeValueMethod.Invoke(operandVM, new object[] { codeEnumValue, resolvedOperand });
                            debugInfo.AppendLine($"[OPERAND-FIX] WriteValue başarılı: {operandText}");
                        }
                        catch (Exception writeEx)
                        {
                            debugInfo.AppendLine($"[OPERAND-FIX] WriteValue hatası: {writeEx.Message}, fallback to manual");
                            // Fallback: Manual operand setting
                            SetOperandManually(operandVM, opcodeName, operandText, debugInfo);
                        }
                    }
                    else
                    {
                        debugInfo.AppendLine("[OPERAND-FIX] WriteValue method not found, using manual");
                        SetOperandManually(operandVM, opcodeName, operandText, debugInfo);
                    }
                }

                // Hata kontrolü
                var hasErrorProp = instructionVMType.GetProperty("HasError");
                if (hasErrorProp != null)
                {
                    var hasError = hasErrorProp.GetValue(instructionVM);
                    debugInfo.AppendLine($"InstructionVM.HasError: {hasError}");
                }
                debugInfo.AppendLine($"InstructionVM oluşturuldu: {opcodeName} {operandText}");
                return instructionVM;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"CreateInstructionVMSimplified error: {ex.Message}");
                return null;
            }
        }

        // MethodDef bulucu (OwnerModule üzerinden)
        private object FindMethodDef(string operandText)
        {
            var module = GetActiveModule();
            if (module == null) return null;
            // operandText ör: "System.String Stimulsoft.Base.Licenses.StiLicenseKey::get_Signature()"
            var match = System.Text.RegularExpressions.Regex.Match(operandText, @"^(?<retType>.+?) (?<type>.+?)::(?<name>.+)$");
            if (!match.Success) return null;
            var typeName = match.Groups["type"].Value;
            var methodName = match.Groups["name"].Value.Split('(')[0];
            var typeDef = module.Find(typeName, false);
            if (typeDef == null) return null;
            return typeDef.Methods.FirstOrDefault(m => m.Name == methodName);
        }

        private object FindFieldDef(string operandText)
        {
            var module = GetActiveModule();
            if (module == null) return null;
            // operandText ör: "System.Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,System.Boolean> Stimulsoft.Base.Licenses.StiLicenseKeyValidator/<>c::<>9__0_0"
            var match = System.Text.RegularExpressions.Regex.Match(operandText, @"^(?<fieldType>.+?) (?<type>.+?)::(?<name>.+)$");
            if (!match.Success) return null;
            var typeName = match.Groups["type"].Value;
            var fieldName = match.Groups["name"].Value;
            var typeDef = module.Find(typeName, false);
            if (typeDef == null) return null;
            return typeDef.Fields.FirstOrDefault(f => f.Name == fieldName);
        }

        private object FindTypeDef(string operandText)
        {
            var module = GetActiveModule();
            if (module == null) return null;
            // operandText ör: "Stimulsoft.Base.Licenses.StiLicenseKey"
            return module.Find(operandText, false);
        }

        // Aktif modülü ve context'i MethodBodyVM üzerinden bulmaya çalışır
        private dnlib.DotNet.ModuleDef GetActiveModule()
        {
            // Aktif IL editör penceresini bul
            foreach (Window window in Application.Current.Windows)
            {
                var windowTypeName = window.GetType().Name;
                if (windowTypeName == "MethodBodyDlg" || windowTypeName.Contains("MethodBody") ||
                    windowTypeName.Contains("EditIL") || windowTypeName.Contains("ILEdit") ||
                    window.Title.Contains("Edit IL") || window.Title.Contains("Method Body"))
                {
                    var dataContext = window.DataContext;
                    if (dataContext != null)
                    {
                        // MethodBodyVM'den OwnerModule veya benzeri property'yi bul
                        var ownerModule = dataContext.GetType().GetProperty("OwnerModule", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance)?.GetValue(dataContext);
                        if (ownerModule is dnlib.DotNet.ModuleDef moduleDef)
                            return moduleDef;
                        // Alternatif: CilBodyVM üzerinden
                        var cilBodyVM = dataContext.GetType().GetProperty("CilBodyVM", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance)?.GetValue(dataContext);
                        if (cilBodyVM != null)
                        {
                            var ownerModule2 = cilBodyVM.GetType().GetProperty("OwnerModule", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance)?.GetValue(cilBodyVM);
                            if (ownerModule2 is dnlib.DotNet.ModuleDef moduleDef2)
                                return moduleDef2;
                        }
                    }
                }
            }
            return null;
        }

        // Kod benzerliğini kontrol eden yardımcı fonksiyon
        private bool IsCodeLike(string text)
        {
            if (string.IsNullOrEmpty(text)) return false;
            if (text.Contains("class ") || text.Contains("void ") || text.Contains("public ") || text.Contains("private ") || text.Contains("namespace "))
                return true;
            if (text.Contains("IL_") || text.Contains("ldloc") || text.Contains("stloc") || text.Contains("call") || text.Contains("ret"))
                return true;
            if (text.Length > 50 && text.Count(c => c == ';') > 2)
                return true;
            return false;
        }

        // TextView'dan metin çıkaran fonksiyon (gerçek kod)
        private string ExtractTextFromTextView(object textViewObj, StringBuilder debugInfo, string context)
        {
            debugInfo.AppendLine($"ExtractTextFromTextView çağrıldı: {context}");
            if (textViewObj == null) return string.Empty;
            try
            {
                // TextBuffer property’sini bul
                var textBufferProp = textViewObj.GetType().GetProperty("TextBuffer");
                var textBuffer = textBufferProp?.GetValue(textViewObj);
                if (textBuffer == null)
                {
                    debugInfo.AppendLine("TextBuffer bulunamadı");
                    return string.Empty;
                }
                // CurrentSnapshot veya CurrentTextSnapshot property’sini bul
                var snapshotProp = textBuffer.GetType().GetProperty("CurrentSnapshot") ?? textBuffer.GetType().GetProperty("CurrentTextSnapshot");
                var snapshot = snapshotProp?.GetValue(textBuffer);
                if (snapshot == null)
                {
                    debugInfo.AppendLine("TextSnapshot bulunamadı");
                    return string.Empty;
                }
                // GetText() metodunu çağır
                var getTextMethod = snapshot.GetType().GetMethod("GetText", System.Type.EmptyTypes);
                if (getTextMethod != null)
                {
                    var text = getTextMethod.Invoke(snapshot, null) as string;
                    debugInfo.AppendLine($"TextView'dan kod okundu, uzunluk: {text?.Length ?? 0}");
                    return text ?? string.Empty;
                }
                debugInfo.AppendLine("GetText metodu bulunamadı");
                return string.Empty;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"ExtractTextFromTextView hata: {ex.Message}");
                return string.Empty;
            }        }

        // IL satırından offset, opcode ve operandı ayırır
        private (string, string, string) ParseILLineComponents(string line)
        {
            if (string.IsNullOrWhiteSpace(line)) return ("", "", "");
            
            // Remove comments from line (everything after //)
            var commentIndex = line.IndexOf("//");
            if (commentIndex >= 0)
            {
                line = line.Substring(0, commentIndex).Trim();
                if (string.IsNullOrWhiteSpace(line)) return ("", "", "");
            }
            
            // Örnek: IL_0000: ldarg.0
            var match = System.Text.RegularExpressions.Regex.Match(line, @"^(IL_\w+):\s*(\S+)(\s+(.+))?");
            if (match.Success)
            {
                var offset = match.Groups[1].Value;
                var opcode = match.Groups[2].Value;
                var operand = match.Groups[4].Success ? match.Groups[4].Value.Trim() : "";
                return (offset, opcode, operand);
            }
            // Sadece opcode ve operand
            var parts = line.Split(new[] { ' ' }, 2);
            if (parts.Length == 2)
                return ("", parts[0], parts[1].Trim());
            if (parts.Length == 1)
                return ("", parts[0], "");
            return ("", "", "");
        }

        // Reflection ile tam tip adını bulır (tüm assembly'lerde arar ve debug loglar)
        private Type FindType(string fullName)
        {
            var type = Type.GetType(fullName);
            if (type != null)
                return type;
            // Yüklü assembly'lerde ara
            foreach (var asm in AppDomain.CurrentDomain.GetAssemblies())
            {
                type = asm.GetType(fullName);
                if (type != null)
                    return type;
            }
            // Alternatif: dnSpy.AsmEditor.x.dll için kısa ad düzeltmesi
            if (fullName.StartsWith("dnSpy.AsmEditor.MethodBody.InstructionVM"))
            {
                foreach (var asm in AppDomain.CurrentDomain.GetAssemblies())
                {
                    if (asm.GetName().Name == "dnSpy.AsmEditor.x")
                    {
                        type = asm.GetType("dnSpy.AsmEditor.MethodBody.InstructionVM");
                        if (type != null)
                            return type;
                    }
                }
            }
            return null;
        }

        // Basit InstructionVM oluşturucu - Geliştirilmiş operand handling
        private object CreateInstructionVMSimplified(string instructionText, System.Text.StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine($"[SIMPLE] InstructionVM from: {instructionText}");
                (string offset, string opcodeName, string operandText) = ParseILLineComponents(instructionText);
                if (string.IsNullOrEmpty(opcodeName))
                {
                    debugInfo.AppendLine($"Parse failed: {instructionText}");
                    return null;
                }

                var instructionVMType = FindType("dnSpy.AsmEditor.MethodBody.InstructionVM, dnSpy.AsmEditor");
                if (instructionVMType == null)
                {
                    debugInfo.AppendLine("InstructionVM tipi bulunamadı");
                    return null;
                }
                var instructionVM = Activator.CreateInstance(instructionVMType);
                if (instructionVM == null) return null;

                OpCode opCode = MapOpcodeToOpCode(opcodeName);
                var codeEnumType = FindType("dnlib.DotNet.Emit.Code");
                object codeEnumValue = null;
                if (codeEnumType != null)
                {
                    codeEnumValue = Enum.Parse(codeEnumType, opCode.Code.ToString());
                    SetProperty(instructionVM, "Code", codeEnumValue);
                }

                if (!string.IsNullOrEmpty(offset))
                {
                    uint offsetVal = 0;
                    if (offset.StartsWith("IL_") && uint.TryParse(offset.Substring(3), System.Globalization.NumberStyles.HexNumber, null, out offsetVal))
                        SetProperty(instructionVM, "Offset", offsetVal);
                }

                // IMPROVED: Operand handling with WriteValue
                var operandVM = GetProperty(instructionVM, "InstructionOperandVM");
                if (operandVM != null && !string.IsNullOrEmpty(operandText))
                {
                    debugInfo.AppendLine($"[SIMPLE-OPERAND] Processing: {operandText}");

                    // Try WriteValue first for proper operand type setting
                    var writeValueMethod = operandVM.GetType().GetMethod("WriteValue");
                    if (writeValueMethod != null && codeEnumValue != null)
                    {
                        try
                        {
                            var resolvedOperand = ResolveOperandAdvanced(opcodeName, operandText, debugInfo);
                            writeValueMethod.Invoke(operandVM, new object[] { codeEnumValue, resolvedOperand });
                            debugInfo.AppendLine($"[SIMPLE-OPERAND] WriteValue success: {operandText}");
                        }
                        catch (Exception writeEx)
                        {
                            debugInfo.AppendLine($"[SIMPLE-OPERAND] WriteValue failed: {writeEx.Message}, using fallback");
                            SetProperty(operandVM, "Other", operandText);
                        }
                    }
                    else
                    {
                        debugInfo.AppendLine("[SIMPLE-OPERAND] WriteValue not available, using Other");
                        SetProperty(operandVM, "Other", operandText);
                    }
                }

                return instructionVM;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"CreateInstructionVMSimplified error: {ex.Message}");
                return null;
            }
        }

        // Branch hedeflerini çözen fonksiyon (temel)
        private void ResolveBranchTargets(IEnumerable<string> lines, List<object> vms, Dictionary<string, object> lineToVmMap, StringBuilder debugInfo)
        {
            debugInfo?.AppendLine("ResolveBranchTargets başlatıldı");
            // Basit: branch opcode'ları için operand string'i offset ise, ilgili InstructionVM'yi ata
            for (int i = 0; i < lines.Count() && i < vms.Count; i++)
            {
                var line = lines.ElementAt(i);
                var vm = vms[i];
                (string offset, string opcode, string operand) = ParseILLineComponents(line);
                if (opcode.ToLower().Contains("br"))
                {
                    if (!string.IsNullOrEmpty(operand) && lineToVmMap.TryGetValue(operand, out var targetVm))
                    {
                        var operandVM = GetProperty(vm, "InstructionOperandVM");
                        if (operandVM != null)
                        {
                            SetProperty(operandVM, "Other", targetVm);
                            debugInfo?.AppendLine($"Branch target çözüldü: {opcode} -> {operand}");
                        }
                    }
                }
            }
        }        // KESIN ÇÖZÜM: dnSpy'nin kendi operand parsing sistemini kullan
        private object ResolveOperandAdvanced(string opcodeName, string operandText, StringBuilder debugInfo)
        {
            if (string.IsNullOrWhiteSpace(operandText)) return null;

            try
            {
                debugInfo?.AppendLine($"[DNSPY-RESOLVE] Processing: {opcodeName} {operandText}");

                // String operand - dnSpy format'ında
                if (operandText.StartsWith("\"") && operandText.EndsWith("\""))
                {
                    var stringValue = operandText.Substring(1, operandText.Length - 2);
                    debugInfo?.AppendLine($"[DNSPY-RESOLVE] String operand: {stringValue}");
                    return stringValue;
                }

                // Numeric operands - primitive types
                if (int.TryParse(operandText, out int intVal))
                {
                    debugInfo?.AppendLine($"[DNSPY-RESOLVE] Int32 operand: {intVal}");
                    return intVal;
                }

                if (long.TryParse(operandText, out long longVal))
                {
                    debugInfo?.AppendLine($"[DNSPY-RESOLVE] Int64 operand: {longVal}");
                    return longVal;
                }

                if (float.TryParse(operandText, out float floatVal))
                {
                    debugInfo?.AppendLine($"[DNSPY-RESOLVE] Single operand: {floatVal}");
                    return floatVal;
                }

                if (double.TryParse(operandText, out double doubleVal))
                {
                    debugInfo?.AppendLine($"[DNSPY-RESOLVE] Double operand: {doubleVal}");
                    return doubleVal;
                }

                // Branch target (IL_XXXX format) - null döndür, sonra çözümlenecek
                if (operandText.StartsWith("IL_"))
                {
                    debugInfo?.AppendLine($"[DNSPY-RESOLVE] Branch target: {operandText}");
                    return null; // Branch target'lar ikinci fazda çözümlenecek
                }

                // CRITICAL: dnSpy'nin kendi member resolution sistemini kullan
                var module = GetActiveModule();
                if (module != null)
                {
                    var resolvedMember = ResolveMemberUsingDnSpySystem(operandText, module, debugInfo);
                    if (resolvedMember != null)
                    {
                        debugInfo?.AppendLine($"[DNSPY-RESOLVE] Member resolved via dnSpy system: {operandText}");
                        return resolvedMember;
                    }
                }

                debugInfo?.AppendLine($"[DNSPY-RESOLVE] Fallback to string: {operandText}");
                return operandText;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[DNSPY-RESOLVE] Error: {ex.Message}");
                return operandText;
            }
        }

        // dnSpy'nin kendi member resolution sistemini kullan
        private object ResolveMemberUsingDnSpySystem(string operandText, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[DNSPY-MEMBER] Resolving: {operandText}");

                // Method pattern: RetType TypeName::MethodName(Params)
                var methodMatch = System.Text.RegularExpressions.Regex.Match(operandText,
                    @"^(?<retType>.+?)\s+(?<typeName>.+?)::(?<methodName>[^(]+)\((?<params>.*)\)$");

                if (methodMatch.Success)
                {
                    var typeName = methodMatch.Groups["typeName"].Value.Trim();
                    var methodName = methodMatch.Groups["methodName"].Value.Trim();

                    debugInfo?.AppendLine($"[DNSPY-MEMBER] Method pattern: {typeName}::{methodName}");

                    // dnSpy'nin Find sistemini kullan
                    var typeDef = module.Find(typeName, false);
                    if (typeDef != null)
                    {
                        var method = typeDef.Methods.FirstOrDefault(m => m.Name == methodName);
                        if (method != null)
                        {
                            debugInfo?.AppendLine($"[DNSPY-MEMBER] Found internal method: {method.FullName}");
                            return method;
                        }
                    }

                    // External method için MemberRef oluştur - dnSpy compatible
                    var memberRef = CreateDnSpyCompatibleMemberRef(typeName, methodName, module, debugInfo);
                    if (memberRef != null)
                    {
                        debugInfo?.AppendLine($"[DNSPY-MEMBER] Created MemberRef: {typeName}::{methodName}");
                        return memberRef;
                    }
                }

                // Field pattern: FieldType TypeName::FieldName
                var fieldMatch = System.Text.RegularExpressions.Regex.Match(operandText,
                    @"^(?<fieldType>.+?)\s+(?<typeName>.+?)::(?<fieldName>.+)$");

                if (fieldMatch.Success)
                {
                    var typeName = fieldMatch.Groups["typeName"].Value.Trim();
                    var fieldName = fieldMatch.Groups["fieldName"].Value.Trim();

                    debugInfo?.AppendLine($"[DNSPY-MEMBER] Field pattern: {typeName}::{fieldName}");

                    var typeDef = module.Find(typeName, false);
                    if (typeDef != null)
                    {
                        var field = typeDef.Fields.FirstOrDefault(f => f.Name == fieldName);
                        if (field != null)
                        {
                            debugInfo?.AppendLine($"[DNSPY-MEMBER] Found field: {field.FullName}");
                            return field;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[DNSPY-MEMBER] Error: {ex.Message}");
                return null;
            }
        }

        // Manual operand setting - WriteValue başarısız olursa kullanılır
        private void SetOperandManually(object operandVM, string opcodeName, string operandText, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[MANUAL-OPERAND] Setting manually: {opcodeName} {operandText}");

                // String operand
                if (operandText.StartsWith("\"") && operandText.EndsWith("\""))
                {
                    var stringValue = operandText.Substring(1, operandText.Length - 2);
                    var stringVM = GetProperty(operandVM, "String");
                    if (stringVM != null)
                    {
                        SetProperty(stringVM, "Value", stringValue);
                        debugInfo?.AppendLine($"[MANUAL-OPERAND] String set: {stringValue}");
                        return;
                    }
                }

                // Numeric operands
                if (int.TryParse(operandText, out int intVal))
                {
                    var int32VM = GetProperty(operandVM, "Int32");
                    if (int32VM != null)
                    {
                        SetProperty(int32VM, "Value", intVal);
                        debugInfo?.AppendLine($"[MANUAL-OPERAND] Int32 set: {intVal}");
                        return;
                    }
                }

                // Branch target veya complex operand
                SetProperty(operandVM, "Other", operandText);
                debugInfo?.AppendLine($"[MANUAL-OPERAND] Other set: {operandText}");
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[MANUAL-OPERAND] Error: {ex.Message}");
            }
        }

        // dnSpy compatible MemberRef oluştur - Doğru assembly reference ile
        private object CreateDnSpyCompatibleMemberRef(string typeName, string methodName, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[DNSPY-MEMBERREF] Creating MemberRef: {typeName}::{methodName}");

                // Assembly reference'ı bul veya oluştur
                var assemblyRef = GetOrCreateAssemblyRef(typeName, module, debugInfo);
                if (assemblyRef == null)
                {
                    debugInfo?.AppendLine("[DNSPY-MEMBERREF] Failed to get assembly reference");
                    return null;
                }

                // TypeRef oluştur
                var typeRef = CreateTypeRef(typeName, assemblyRef, module, debugInfo);
                if (typeRef == null)
                {
                    debugInfo?.AppendLine("[DNSPY-MEMBERREF] Failed to create TypeRef");
                    return null;
                }

                // Method signature oluştur (basit version)
                var methodSig = CreateMethodSignature(module, debugInfo);
                if (methodSig == null)
                {
                    debugInfo?.AppendLine("[DNSPY-MEMBERREF] Failed to create method signature");
                    return null;
                }

                // MemberRef oluştur
                var memberRefType = FindType("dnlib.DotNet.MemberRefUser");
                if (memberRefType != null)
                {
                    var memberRef = Activator.CreateInstance(memberRefType, module, methodName, methodSig, typeRef);
                    debugInfo?.AppendLine($"[DNSPY-MEMBERREF] Created MemberRef: {memberRef}");
                    return memberRef;
                }

                debugInfo?.AppendLine("[DNSPY-MEMBERREF] MemberRefUser type not found");
                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[DNSPY-MEMBERREF] Error: {ex.Message}");
                return null;
            }
        }

        // Assembly reference bul veya oluştur
        private object GetOrCreateAssemblyRef(string typeName, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                // System types için mscorlib veya System.Core kullan
                string assemblyName = "mscorlib";
                if (typeName.Contains("System.Linq") || typeName.Contains("System.Core"))
                    assemblyName = "System.Core";
                else if (typeName.Contains("System."))
                    assemblyName = "mscorlib";

                debugInfo?.AppendLine($"[ASSEMBLY-REF] Looking for assembly: {assemblyName}");

                // Mevcut assembly reference'ları kontrol et - reflection ile
                var assemblyRefsProperty = module.GetType().GetProperty("AssemblyRefs");
                if (assemblyRefsProperty != null)
                {
                    var assemblyRefs = assemblyRefsProperty.GetValue(module) as System.Collections.IList;
                    if (assemblyRefs != null)
                    {
                        foreach (var asmRef in assemblyRefs)
                        {
                            var nameProperty = asmRef.GetType().GetProperty("Name");
                            var name = nameProperty?.GetValue(asmRef)?.ToString();
                            if (name != null && name.Contains(assemblyName))
                            {
                                debugInfo?.AppendLine($"[ASSEMBLY-REF] Found existing: {name}");
                                return asmRef;
                            }
                        }

                        // Yeni assembly reference oluştur - dnSpy compatible
                        var assemblyRefUserType = FindType("dnlib.DotNet.AssemblyRefUser");
                        if (assemblyRefUserType != null)
                        {
                            try
                            {
                                // AssemblyRefUser constructor: (string name)
                                var newAsmRef = Activator.CreateInstance(assemblyRefUserType, assemblyName);
                                if (newAsmRef != null)
                                {
                                    // Set additional properties for proper assembly reference
                                    var versionProperty = assemblyRefUserType.GetProperty("Version");
                                    var cultureProperty = assemblyRefUserType.GetProperty("Culture");
                                    var publicKeyTokenProperty = assemblyRefUserType.GetProperty("PublicKeyToken");

                                    if (assemblyName == "mscorlib")
                                    {
                                        versionProperty?.SetValue(newAsmRef, new Version(4, 0, 0, 0));
                                        cultureProperty?.SetValue(newAsmRef, "");
                                        // mscorlib public key token
                                        var mscorlibToken = new byte[] { 0xb7, 0x7a, 0x5c, 0x56, 0x19, 0x34, 0xe0, 0x89 };
                                        publicKeyTokenProperty?.SetValue(newAsmRef, mscorlibToken);
                                    }
                                    else if (assemblyName == "System.Core")
                                    {
                                        versionProperty?.SetValue(newAsmRef, new Version(4, 0, 0, 0));
                                        cultureProperty?.SetValue(newAsmRef, "");
                                        // System.Core public key token (same as mscorlib)
                                        var systemCoreToken = new byte[] { 0xb7, 0x7a, 0x5c, 0x56, 0x19, 0x34, 0xe0, 0x89 };
                                        publicKeyTokenProperty?.SetValue(newAsmRef, systemCoreToken);
                                    }

                                    assemblyRefs.Add(newAsmRef);
                                    debugInfo?.AppendLine($"[ASSEMBLY-REF] Created new with proper metadata: {assemblyName}");
                                    return newAsmRef;
                                }
                            }
                            catch (Exception ex)
                            {
                                debugInfo?.AppendLine($"[ASSEMBLY-REF] Creation failed: {ex.Message}");
                            }
                        }
                    }
                }

                debugInfo?.AppendLine("[ASSEMBLY-REF] Failed to create assembly reference");
                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[ASSEMBLY-REF] Error: {ex.Message}");
                return null;
            }
        }

        // TypeRef oluştur
        private object CreateTypeRef(string typeName, object assemblyRef, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[TYPE-REF] Creating TypeRef: {typeName}");

                // Namespace ve type name'i ayır
                string namespaceName = "";
                string simpleTypeName = typeName;

                var lastDotIndex = typeName.LastIndexOf('.');
                if (lastDotIndex > 0)
                {
                    namespaceName = typeName.Substring(0, lastDotIndex);
                    simpleTypeName = typeName.Substring(lastDotIndex + 1);
                }

                // Generic type handling
                if (simpleTypeName.Contains('`'))
                {
                    // Generic type - `1, `2 etc.
                    debugInfo?.AppendLine($"[TYPE-REF] Generic type detected: {simpleTypeName}");
                }

                // TypeRefUser oluştur
                var typeRefUserType = FindType("dnlib.DotNet.TypeRefUser");
                if (typeRefUserType != null)
                {
                    var typeRef = Activator.CreateInstance(typeRefUserType, module, namespaceName, simpleTypeName, assemblyRef);
                    debugInfo?.AppendLine($"[TYPE-REF] Created TypeRef: {namespaceName}.{simpleTypeName}");
                    return typeRef;
                }

                debugInfo?.AppendLine("[TYPE-REF] TypeRefUser type not found");
                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[TYPE-REF] Error: {ex.Message}");
                return null;
            }
        }

        // Method signature oluştur
        private object CreateMethodSignature(dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine("[METHOD-SIG] Creating method signature");

                // MethodSig oluştur - basit version (void return, no params)
                var methodSigType = FindType("dnlib.DotNet.MethodSig");
                if (methodSigType == null)
                {
                    debugInfo?.AppendLine("[METHOD-SIG] MethodSig type not found");
                    return null;
                }

                // Void type için TypeSig
                var voidTypeSig = GetVoidTypeSig(module, debugInfo);
                if (voidTypeSig == null)
                {
                    debugInfo?.AppendLine("[METHOD-SIG] Failed to get void TypeSig");
                    return null;
                }

                // CallingConvention
                var callingConvType = FindType("dnlib.DotNet.CallingConvention");
                object callingConv = null;
                if (callingConvType != null)
                {
                    callingConv = Enum.Parse(callingConvType, "Default");
                }

                // MethodSig oluştur
                var methodSig = Activator.CreateInstance(methodSigType, callingConv, 0, voidTypeSig);
                debugInfo?.AppendLine("[METHOD-SIG] Created method signature");
                return methodSig;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[METHOD-SIG] Error: {ex.Message}");
                return null;
            }
        }

        // Void TypeSig al
        private object GetVoidTypeSig(dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                // module.CorLibTypes.Void kullan
                var corLibTypes = GetProperty(module, "CorLibTypes");
                if (corLibTypes != null)
                {
                    var voidTypeSig = GetProperty(corLibTypes, "Void");
                    if (voidTypeSig != null)
                    {
                        debugInfo?.AppendLine("[VOID-TYPE] Got void TypeSig from CorLibTypes");
                        return voidTypeSig;
                    }
                }

                debugInfo?.AppendLine("[VOID-TYPE] Failed to get void TypeSig");
                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[VOID-TYPE] Error: {ex.Message}");
                return null;
            }
        }



        // Gelişmiş operand çözümleme (method, field, type referansları için) - Generic Support
        private object ResolveMethodOperand(string operandText, StringBuilder debugInfo)
        {
            var module = GetActiveModule();
            if (module == null) return null;

            try
            {
                debugInfo?.AppendLine($"[GENERIC-RESOLVE] Resolving method: {operandText}");

                // Generic method pattern: RetType TypeName::MethodName<GenericArgs>(Parameters)
                var genericMatch = System.Text.RegularExpressions.Regex.Match(operandText, 
                    @"^(?<retType>.+?)\s+(?<typeName>.+?)::(?<methodName>[^<\(]+)(?:<(?<genericArgs>[^>]+)>)?\((?<params>.*)\)$");

                if (genericMatch.Success)
                {
                    var typeName = genericMatch.Groups["typeName"].Value.Trim();
                    var methodName = genericMatch.Groups["methodName"].Value.Trim();
                    var genericArgs = genericMatch.Groups["genericArgs"].Value.Trim();
                    var parameters = genericMatch.Groups["params"].Value.Trim();

                    debugInfo?.AppendLine($"[GENERIC-PARSE] Type: {typeName}, Method: {methodName}, GenericArgs: {genericArgs}");

                    // Constructor handling
                    if (methodName == ".ctor")
                    {
                        return ResolveConstructorOperand(typeName, genericArgs, parameters, module, debugInfo);
                    }

                    // Regular method handling
                    return ResolveRegularMethodOperand(typeName, methodName, genericArgs, parameters, module, debugInfo);
                }

                // Fallback to simple method resolution
                return FindMethodDef(operandText);
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[GENERIC-ERROR] ResolveMethodOperand error: {ex.Message}");
                return FindMethodDef(operandText);
            }
        }        // Constructor operand resolution
        private object ResolveConstructorOperand(string typeName, string genericArgs, string parameters, object module, StringBuilder debugInfo)
        {
            try
            {
                var moduleObj = module as dnlib.DotNet.ModuleDef;
                if (moduleObj == null) return null;

                debugInfo?.AppendLine($"[CTOR-RESOLVE] Resolving constructor for type: {typeName}");

                // Handle generic types like System.Func`2<T1,T2>
                if (typeName.Contains('`') && typeName.Contains('<'))
                {
                    debugInfo?.AppendLine($"[CTOR-GENERIC] Detected generic type with inline args: {typeName}");
                    
                    // Parse: System.Func`2<Type1,Type2> 
                    var match = System.Text.RegularExpressions.Regex.Match(typeName, @"^(.+?`\d+)<(.+)>$");
                    if (match.Success)
                    {
                        var baseTypeName = match.Groups[1].Value; // System.Func`2
                        var inlineGenericArgs = match.Groups[2].Value; // Type1,Type2
                        
                        debugInfo?.AppendLine($"[CTOR-GENERIC] Base type: {baseTypeName}, Inline args: {inlineGenericArgs}");
                        
                        // Create TypeRef for the instantiated generic type
                        var genericTypeRef = CreateGenericInstantiatedTypeRef(baseTypeName, inlineGenericArgs, moduleObj, debugInfo);
                        if (genericTypeRef != null)
                        {
                            return CreateMemberRefForConstructor(genericTypeRef, parameters, moduleObj, debugInfo);
                        }
                        
                        // Fallback to MethodSpec approach
                        return CreateGenericConstructorMethodSpec(baseTypeName, inlineGenericArgs, parameters, moduleObj, debugInfo);
                    }
                }

                // Generic type handling with separate args
                if (!string.IsNullOrEmpty(genericArgs))
                {
                    debugInfo?.AppendLine($"[CTOR-GENERIC] Creating MethodSpec for generic constructor");
                    
                    // Create TypeRef for the instantiated generic type
                    var genericTypeRef = CreateGenericInstantiatedTypeRef(typeName, genericArgs, moduleObj, debugInfo);
                    if (genericTypeRef != null)
                    {
                        return CreateMemberRefForConstructor(genericTypeRef, parameters, moduleObj, debugInfo);
                    }
                    
                    return CreateGenericConstructorMethodSpec(typeName, genericArgs, parameters, moduleObj, debugInfo);
                }

                // Non-generic constructor
                var typeDef = moduleObj.Find(typeName, false);
                if (typeDef != null)
                {
                    var constructor = typeDef.Methods.FirstOrDefault(m => m.IsConstructor && m.Name == ".ctor");
                    if (constructor != null)
                    {
                        debugInfo?.AppendLine($"[CTOR-FOUND] Constructor found: {constructor.FullName}");
                        return constructor;
                    }
                }

                // Try external constructor
                debugInfo?.AppendLine($"[CTOR-EXTERNAL] Trying external constructor for: {typeName}");
                var typeRef = CreateTypeRef(typeName, moduleObj, debugInfo);
                if (typeRef != null)
                {
                    return CreateMemberRefForConstructor(typeRef, parameters, moduleObj, debugInfo);
                }

                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[CTOR-ERROR] ResolveConstructorOperand error: {ex.Message}");
                return null;
            }
        }        // Regular method operand resolution
        private object ResolveRegularMethodOperand(string typeName, string methodName, string genericArgs, string parameters, object module, StringBuilder debugInfo)
        {
            try
            {
                var moduleObj = module as dnlib.DotNet.ModuleDef;
                if (moduleObj == null) return null;

                debugInfo?.AppendLine($"[METHOD-RESOLVE] Resolving method: {typeName}::{methodName}");

                // Generic method handling
                if (!string.IsNullOrEmpty(genericArgs))
                {
                    debugInfo?.AppendLine($"[METHOD-GENERIC] Creating MethodSpec for generic method: {methodName}<{genericArgs}>");
                    
                    // Try internal method first
                    var typeDef = moduleObj.Find(typeName, false);
                    if (typeDef != null)
                    {
                        var method = typeDef.Methods.FirstOrDefault(m => m.Name == methodName);
                        if (method != null)
                        {
                            debugInfo?.AppendLine($"[METHOD-GENERIC] Found internal method: {method.FullName}");
                            return CreateMethodSpecForGenericInstantiation(method, genericArgs, moduleObj, debugInfo);
                        }
                    }
                    
                    // External generic method via MemberRef + MethodSpec
                    debugInfo?.AppendLine($"[METHOD-GENERIC] Trying external generic method");
                    return CreateMemberRefForGenericMethod(typeName, methodName, genericArgs, parameters, moduleObj, debugInfo);
                }                // Non-generic method
                var typeDef2 = moduleObj.Find(typeName, false);
                if (typeDef2 != null)
                {
                    var method = typeDef2.Methods.FirstOrDefault(m => m.Name == methodName);
                    if (method != null)
                    {
                        debugInfo?.AppendLine($"[METHOD-FOUND] Method found: {method.FullName}");
                        return method;
                    }
                }

                // Try external method via MemberRef
                debugInfo?.AppendLine($"[METHOD-EXTERNAL] Trying external method: {typeName}::{methodName}");
                return CreateMemberRef(typeName, methodName, parameters, moduleObj, debugInfo);
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[METHOD-ERROR] ResolveRegularMethodOperand error: {ex.Message}");
                return null;
            }
        }

        // dnSpy compatible MethodSpec creation helpers
        private object CreateMethodSpecForGenericInstantiation(object method, string genericArgs, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                // dnlib MethodSpec creation using reflection for compatibility
                var methodDefType = FindType("dnlib.DotNet.MethodDef");
                var methodSpecType = FindType("dnlib.DotNet.MethodSpec");
                var genericInstMethodSigType = FindType("dnlib.DotNet.GenericInstMethodSig");

                if (methodDefType == null || methodSpecType == null || genericInstMethodSigType == null)
                {
                    debugInfo?.AppendLine("[METHODSPEC] Required dnlib types not found");
                    return null;
                }

                // Parse generic arguments
                var genericTypes = ParseGenericTypeArguments(genericArgs, module, debugInfo);
                if (genericTypes == null || genericTypes.Count == 0)
                {
                    debugInfo?.AppendLine("[METHODSPEC] Failed to parse generic arguments");
                    return null;
                }

                // Create GenericInstMethodSig
                var genericInstMethodSig = Activator.CreateInstance(genericInstMethodSigType);
                var genericArgsProperty = genericInstMethodSigType.GetProperty("GenericArguments");
                genericArgsProperty?.SetValue(genericInstMethodSig, genericTypes);

                // Create MethodSpec
                var methodSpec = Activator.CreateInstance(methodSpecType);
                var methodProperty = methodSpecType.GetProperty("Method");
                var instantiationProperty = methodSpecType.GetProperty("GenericInstMethodSig");

                methodProperty?.SetValue(methodSpec, method);
                instantiationProperty?.SetValue(methodSpec, genericInstMethodSig);                debugInfo?.AppendLine($"[METHODSPEC] Successfully created MethodSpec with generic arguments: {genericArgs}");
                return methodSpec;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[METHODSPEC-ERROR] CreateMethodSpecForGenericInstantiation: {ex.Message}");
                return method; // Fallback to base method
            }
        }

        // Generic constructor MethodSpec creation
        private object CreateGenericConstructorMethodSpec(string typeName, string genericArgs, string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[METHODSPEC-CTOR] Creating MethodSpec for: {typeName}<{genericArgs}>::.ctor({parameters})");

                // Find base generic type definition
                var baseTypeName = typeName.Split('`')[0]; // Remove generic suffix
                var typeDef = module.Find(baseTypeName, false);
                
                if (typeDef == null)
                {
                    // Try to import external type
                    var typeRef = CreateTypeRef(typeName, module, debugInfo);
                    if (typeRef != null)
                    {
                        debugInfo?.AppendLine($"[METHODSPEC-CTOR] Created TypeRef for external type: {typeName}");
                        return CreateMemberRefForConstructor(typeRef, parameters, module, debugInfo);
                    }
                    return null;
                }

                // Find constructor
                var constructor = typeDef.Methods.FirstOrDefault(m => m.IsConstructor && m.Name == ".ctor");
                if (constructor == null)
                {
                    debugInfo?.AppendLine($"[METHODSPEC-CTOR] Constructor not found in type: {typeName}");
                    return null;
                }

                // Create MethodSpec with generic instantiation - dnSpy compatible approach
                return CreateMethodSpecForGenericInstantiation(constructor, genericArgs, module, debugInfo);
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[METHODSPEC-CTOR-ERROR] {ex.Message}");
                return null;
            }
        }

        // Generic method MethodSpec creation
        private object CreateGenericMethodMethodSpec(string typeName, string methodName, string genericArgs, string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[METHODSPEC-METHOD] Creating MethodSpec for: {typeName}::{methodName}<{genericArgs}>({parameters})");

                // Find base type
                var typeDef = module.Find(typeName, false);
                if (typeDef == null)
                {
                    // Try external method via MemberRef
                    return CreateMemberRefForGenericMethod(typeName, methodName, genericArgs, parameters, module, debugInfo);
                }

                // Find method
                var method = typeDef.Methods.FirstOrDefault(m => m.Name == methodName);
                if (method == null)
                {
                    debugInfo?.AppendLine($"[METHODSPEC-METHOD] Method not found: {methodName}");
                    return null;
                }                // Create MethodSpec with generic instantiation
                return CreateMethodSpecForGenericInstantiation(method, genericArgs, module, debugInfo);
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[METHODSPEC-METHOD-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create method signature from parameter string
        private object CreateMethodSigFromParameters(string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {                debugInfo?.AppendLine($"[METHODSIG] Creating MethodSig for parameters: {parameters}");

                var methodSigType = FindType("dnlib.DotNet.MethodSig");
                if (methodSigType == null) return null;

                // Parse parameter types - use smart splitting to handle generics
                var paramTypes = new List<object>();
                if (!string.IsNullOrEmpty(parameters) && parameters.Trim() != "")
                {
                    var paramStrings = SmartSplitGenericArguments(parameters);
                    foreach (var paramString in paramStrings)
                    {
                        var trimmed = paramString.Trim();
                        if (!string.IsNullOrEmpty(trimmed))
                        {
                            var typeSig = CreateTypeSigFromString(trimmed, module, debugInfo);
                            if (typeSig != null)
                            {
                                paramTypes.Add(typeSig);
                            }
                        }
                    }
                }

                // Create method signature with void return type
                var voidTypeSig = CreateVoidTypeSig(module);
                var callingConvention = FindCallingConvention("Default");

                // Try creating MethodSig using constructor
                var constructor = methodSigType.GetConstructor(new[] { typeof(byte), typeof(uint) });
                if (constructor != null)
                {
                    var methodSig = constructor.Invoke(new object[] { (byte)callingConvention, (uint)paramTypes.Count });
                    
                    // Set parameters
                    var paramsProperty = methodSigType.GetProperty("Params");
                    if (paramsProperty != null)
                    {
                        var paramsList = paramsProperty.GetValue(methodSig);
                        if (paramsList != null)
                        {
                            var listType = paramsList.GetType();
                            var addMethod = listType.GetMethod("Add");
                            if (addMethod != null)
                            {
                                foreach (var param in paramTypes)
                                {
                                    addMethod.Invoke(paramsList, new[] { param });
                                }
                            }
                        }
                    }

                    // Set return type
                    var retTypeProperty = methodSigType.GetProperty("RetType");
                    retTypeProperty?.SetValue(methodSig, voidTypeSig);

                    debugInfo?.AppendLine($"[METHODSIG] Successfully created MethodSig with {paramTypes.Count} parameters");
                    return methodSig;
                }

                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[METHODSIG-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create constructor signature from parameter string  
        private object CreateConstructorSigFromParameters(string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[CTORSIG] Creating constructor MethodSig for parameters: {parameters}");

                var methodSigType = FindType("dnlib.DotNet.MethodSig");
                if (methodSigType == null) return null;

                // Parse parameter types
                var paramTypes = new List<object>();
                if (!string.IsNullOrEmpty(parameters) && parameters.Trim() != "")
                {
                    var paramStrings = parameters.Split(',');
                    foreach (var paramString in paramStrings)
                    {
                        var trimmed = paramString.Trim();
                        if (!string.IsNullOrEmpty(trimmed))
                        {
                            var typeSig = CreateTypeSigFromString(trimmed, module, debugInfo);
                            if (typeSig != null)
                            {
                                paramTypes.Add(typeSig);
                            }
                        }
                    }
                }

                // Create method signature with void return type (constructors return void)
                var voidTypeSig = CreateVoidTypeSig(module);
                var callingConvention = FindCallingConvention("HasThis"); // Constructors have 'this'

                // Try creating MethodSig using constructor
                var constructor = methodSigType.GetConstructor(new[] { typeof(byte), typeof(uint) });
                if (constructor != null)
                {
                    var methodSig = constructor.Invoke(new object[] { (byte)callingConvention, (uint)paramTypes.Count });
                    
                    // Set parameters
                    var paramsProperty = methodSigType.GetProperty("Params");
                    if (paramsProperty != null)
                    {
                        var paramsList = paramsProperty.GetValue(methodSig);
                        if (paramsList != null)
                        {
                            var listType = paramsList.GetType();
                            var addMethod = listType.GetMethod("Add");
                            if (addMethod != null)
                            {
                                foreach (var param in paramTypes)
                                {
                                    addMethod.Invoke(paramsList, new[] { param });
                                }
                            }
                        }
                    }

                    // Set return type to void
                    var retTypeProperty = methodSigType.GetProperty("RetType");
                    retTypeProperty?.SetValue(methodSig, voidTypeSig);

                    debugInfo?.AppendLine($"[CTORSIG] Successfully created constructor MethodSig with {paramTypes.Count} parameters");
                    return methodSig;
                }

                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[CTORSIG-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create void TypeSig
        private object CreateVoidTypeSig(dnlib.DotNet.ModuleDef module)
        {
            try
            {
                var corLibTypeSigType = FindType("dnlib.DotNet.CorLibTypeSig");
                if (corLibTypeSigType == null) return null;

                var elementTypeEnum = FindType("dnlib.DotNet.ElementType");
                if (elementTypeEnum == null) return null;

                var voidElementType = Enum.Parse(elementTypeEnum, "Void");
                var constructor = corLibTypeSigType.GetConstructor(new[] { elementTypeEnum });
                
                if (constructor != null)
                {
                    return constructor.Invoke(new[] { voidElementType });
                }
            }
            catch
            {
                return null;
            }
            return null;
        }

        // Find calling convention
        private int FindCallingConvention(string convention)
        {
            try            {
                var callingConventionType = FindType("dnlib.DotNet.CallingConvention");
                if (callingConventionType == null) return 0;

                switch (convention)
                {
                    case "Default": return 0;
                    case "HasThis": return 0x20;
                    default: return 0;
                }
            }
            catch
            {
                return 0;
            }
        }

        // Helper metodları - Reflection ve utility
        private object GetProperty(object obj, string propertyName)
        {
            return obj?.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance)?.GetValue(obj);
        }

        private void SetProperty(object obj, string propertyName, object value)
        {
            obj?.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance)?.SetValue(obj, value);
        }

        private OpCode MapOpcodeToOpCode(string opcodeName)
        {
            return MapToOpCode(opcodeName);
        }

        // dnlib OpCodes listesinden dinamik ve toleranslı olarak opcode bulur
        private OpCode MapToOpCode(string opcodeName)
        {
            if (string.IsNullOrWhiteSpace(opcodeName))
                return OpCodes.Nop;
            try
            {
                // Tüm OpCodes alanlarını gez ve en yakın eşleşeni bul
                var fields = typeof(OpCodes).GetFields(BindingFlags.Public | BindingFlags.Static);
                foreach (var field in fields)
                {
                    // Tam eşleşme
                    if (string.Equals(field.Name, opcodeName, StringComparison.OrdinalIgnoreCase))
                        return (OpCode)field.GetValue(null);
                    // Kısa ad düzeltmeleri (örn. brtrue.s -> brtrue, callvirt.s -> callvirt)
                    if (opcodeName.EndsWith(".s") && string.Equals(field.Name, opcodeName.Replace(".s", ""), StringComparison.OrdinalIgnoreCase))
                        return (OpCode)field.GetValue(null);
                    // Alt çizgi, tire, nokta toleransı
                    if (string.Equals(field.Name.Replace("_", "").Replace(".", "").Replace("-", "").ToLowerInvariant(),
                                      opcodeName.Replace("_", "").Replace(".", "").Replace("-", "").ToLowerInvariant()))
                        return (OpCode)field.GetValue(null);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"MapToOpCode error: {opcodeName} - {ex.Message}");
            }
            return OpCodes.Nop;
        }

        // KESIN ÇÖZÜM: IL kodunu iki fazlı olarak editöre uygular - Branch target çözümleme ile
        private async Task WriteToILEditorWithTwoPhase(string newCode)
        {
            var debugPath = @"C:\Users\<USER>\Desktop\dnspyai-son-4\dnspyai-il-uygula-debug.log";
            var debugInfo = new System.Text.StringBuilder();
            debugInfo.AppendLine("=== WriteToILEditorWithTwoPhase BAŞLADI (KESIN ÇÖZÜM) ===");
            debugInfo.AppendLine($"Kod uzunluğu: {newCode?.Length ?? 0}");
            try
            {
                foreach (Window window in Application.Current.Windows)
                {
                    var windowTypeName = window.GetType().Name;
                    var dataContext = window.DataContext;
                    debugInfo.AppendLine($"Window: {windowTypeName}, DataContext: {dataContext?.GetType().Name ?? "null"}");
                    if (windowTypeName == "MethodBodyDlg" || windowTypeName.Contains("MethodBody") ||
                        windowTypeName.Contains("EditIL") || windowTypeName.Contains("ILEdit") ||
                        window.Title.Contains("Edit IL") || window.Title.Contains("Method Body"))
                    {
                        if (dataContext != null)
                        {
                            debugInfo.AppendLine($"IL editör context bulundu: {windowTypeName}");

                            // CRITICAL: İki fazlı sistem kullan
                            var result = WriteToILEditorWithProperTwoPhase(dataContext, newCode, debugInfo);
                            debugInfo.AppendLine($"WriteToILEditorWithProperTwoPhase sonucu: {result}");
                            System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
                            return;
                        }
                    }
                }
                debugInfo.AppendLine("Uygun IL editör context bulunamadı!");
                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"WriteToILEditorWithTwoPhase hata: {ex.Message}");
                System.IO.File.WriteAllText(debugPath, debugInfo.ToString());
            }
            await Task.CompletedTask;
        }

        // KESIN ÇÖZÜM: Gerçek iki fazlı IL editör yazma sistemi
        private bool WriteToILEditorWithProperTwoPhase(object dataContext, string newCode, StringBuilder debugInfo)
        {
            try
            {
                debugInfo.AppendLine("[TWO-PHASE] Starting proper two-phase IL writing");

                // CilBodyVM'yi al
                var cilBodyVM = GetProperty(dataContext, "CilBodyVM");
                if (cilBodyVM == null)
                {
                    debugInfo.AppendLine("[TWO-PHASE] CilBodyVM not found");
                    return false;
                }

                // InstructionsListVM collection'ı al
                var instructions = GetProperty(cilBodyVM, "InstructionsListVM");
                if (instructions == null)
                {
                    debugInfo.AppendLine("[TWO-PHASE] InstructionsListVM collection not found");
                    return false;
                }

                // Clear existing instructions
                var clearMethod = instructions.GetType().GetMethod("Clear");
                clearMethod?.Invoke(instructions, null);
                debugInfo.AppendLine("[TWO-PHASE] Cleared existing instructions");

                // Parse IL lines
                var ilLines = FilterILBodyFromAIResponse(newCode, debugInfo);
                var instructionVMs = new List<object>();
                var branchTargetMap = new Dictionary<string, int>(); // IL_XXXX -> instruction index

                // PHASE 1: Create all instructions and build offset map
                debugInfo.AppendLine("[TWO-PHASE] PHASE 1: Creating instructions");
                int instructionIndex = 0;
                foreach (var line in ilLines)
                {
                    var instructionVM = CreateInstructionVMWithDnSpySystem(line, debugInfo);
                    if (instructionVM != null)
                    {
                        instructionVMs.Add(instructionVM);

                        // Extract offset for branch target mapping
                        var (offset, opcodeName, operandText) = ParseILLineComponents(line);
                        if (!string.IsNullOrEmpty(offset))
                        {
                            branchTargetMap[offset] = instructionIndex;
                            debugInfo.AppendLine($"[TWO-PHASE] Mapped {offset} -> instruction {instructionIndex}");
                        }

                        instructionIndex++;
                    }
                }

                // PHASE 2: Resolve branch targets
                debugInfo.AppendLine("[TWO-PHASE] PHASE 2: Resolving branch targets");
                for (int i = 0; i < instructionVMs.Count; i++)
                {
                    var instructionVM = instructionVMs[i];
                    ResolveBranchTargetsForInstruction(instructionVM, instructionVMs, branchTargetMap, debugInfo);
                }

                // PHASE 3: Add instructions to collection
                debugInfo.AppendLine("[TWO-PHASE] PHASE 3: Adding instructions to collection");
                var addMethod = instructions.GetType().GetMethod("Add");
                foreach (var instructionVM in instructionVMs)
                {
                    addMethod?.Invoke(instructions, new[] { instructionVM });
                }

                debugInfo.AppendLine($"[TWO-PHASE] Successfully added {instructionVMs.Count} instructions");
                return true;
            }
            catch (Exception ex)
            {
                debugInfo.AppendLine($"[TWO-PHASE] Error: {ex.Message}");
                return false;
            }
        }

        // AI'dan gelen IL gövdesini satırlara böler ve dnSpy format'ına çevirir
        private IEnumerable<string> FilterILBodyFromAIResponse(string code, StringBuilder debugInfo)
        {
            if (string.IsNullOrWhiteSpace(code))
            {
                debugInfo?.AppendLine("FilterILBodyFromAIResponse: Kod boş!");
                return new string[0];
            }
            // Satır satır ayır, boş ve yorum satırlarını atla
            var lines = code.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(l => l.Trim())
                .Where(l => !string.IsNullOrWhiteSpace(l) && !l.StartsWith("//"))
                .Select(l => ConvertAIFormatToDnSpyFormat(l, debugInfo)); // CRITICAL: Format conversion

            var arr = lines.ToArray();
            debugInfo?.AppendLine($"FilterILBodyFromAIResponse: {arr.Length} satır bulundu ve dnSpy format'ına çevrildi.");
            return arr;
        }

        // KESIN ÇÖZÜM: AI format'ını dnSpy format'ına çevir
        private string ConvertAIFormatToDnSpyFormat(string line, StringBuilder debugInfo)
        {
            try
            {
                // Parse IL line components
                var (offset, opcode, operand) = ParseILLineComponents(line);
                if (string.IsNullOrEmpty(opcode)) return line;

                // Convert operand to dnSpy format
                var convertedOperand = ConvertOperandToDnSpyFormat(operand, debugInfo);

                // Reconstruct line
                if (!string.IsNullOrEmpty(offset))
                {
                    return $"{offset}: {opcode} {convertedOperand}".Trim();
                }
                else
                {
                    return $"{opcode} {convertedOperand}".Trim();
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[FORMAT-CONVERT] Error converting line: {line} - {ex.Message}");
                return line; // Return original if conversion fails
            }
        }

        // KESIN ÇÖZÜM: Operand'ı dnSpy format'ına çevir
        private string ConvertOperandToDnSpyFormat(string operand, StringBuilder debugInfo)
        {
            if (string.IsNullOrEmpty(operand)) return operand;

            try
            {
                // Method signature conversion
                if (operand.Contains("::"))
                {
                    // Convert: System.Void System.Func`2<...>::.ctor(System.Object,System.IntPtr)
                    // To:      instance void class [mscorlib]System.Func`2<...>::.ctor(object, native int)

                    var converted = operand;

                    // Convert return types and parameter types
                    converted = ConvertTypeNames(converted);

                    // Add assembly references for System types
                    converted = AddAssemblyReferences(converted);

                    // Add instance/static modifiers for methods
                    if (converted.Contains("::"))
                    {
                        converted = AddMethodModifiers(converted);
                    }

                    debugInfo?.AppendLine($"[FORMAT-CONVERT] {operand} -> {converted}");
                    return converted;
                }

                // Field signature conversion
                if (operand.Contains(" ") && !operand.StartsWith("IL_"))
                {
                    var converted = ConvertTypeNames(operand);
                    converted = AddAssemblyReferences(converted);
                    debugInfo?.AppendLine($"[FORMAT-CONVERT] Field: {operand} -> {converted}");
                    return converted;
                }

                return operand;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[FORMAT-CONVERT] Error: {ex.Message}");
                return operand;
            }
        }

        // Type name conversion: System.Void -> void, System.Object -> object, etc.
        private string ConvertTypeNames(string text)
        {
            var conversions = new Dictionary<string, string>
            {
                { "System.Void", "void" },
                { "System.Object", "object" },
                { "System.IntPtr", "native int" },
                { "System.Boolean", "bool" },
                { "System.String", "string" },
                { "System.Int32", "int32" },
                { "System.Int64", "int64" },
                { "System.Single", "float32" },
                { "System.Double", "float64" },
                { "System.Byte", "uint8" },
                { "System.SByte", "int8" },
                { "System.Int16", "int16" },
                { "System.UInt16", "uint16" },
                { "System.UInt32", "uint32" },
                { "System.UInt64", "uint64" }
            };

            foreach (var conversion in conversions)
            {
                text = text.Replace(conversion.Key, conversion.Value);
            }

            return text;
        }

        // Add assembly references: System.Func`2 -> [mscorlib]System.Func`2
        private string AddAssemblyReferences(string text)
        {
            // System.Linq types -> [System.Core]
            if (text.Contains("System.Linq."))
            {
                text = text.Replace("System.Linq.", "[System.Core]System.Linq.");
            }

            // System.Collections.Generic types -> [mscorlib]
            if (text.Contains("System.Collections.Generic."))
            {
                text = text.Replace("System.Collections.Generic.", "[mscorlib]System.Collections.Generic.");
            }

            // Other System types -> [mscorlib]
            if (text.Contains("System.Func`"))
            {
                text = text.Replace("System.Func`", "[mscorlib]System.Func`");
            }

            if (text.Contains("System.Action`"))
            {
                text = text.Replace("System.Action`", "[mscorlib]System.Action`");
            }

            // CRITICAL: Add 'class' prefix for custom types in generics
            text = AddClassPrefixToGenericTypes(text);

            return text;
        }

        // KESIN ÇÖZÜM: Generic type arguments'a 'class' prefix ekle
        private string AddClassPrefixToGenericTypes(string text)
        {
            try
            {
                // Pattern: Type`N<Arg1,Arg2> -> Type`N<class Arg1, class Arg2>
                var genericPattern = @"(`\d+)<([^>]+)>";
                var matches = System.Text.RegularExpressions.Regex.Matches(text, genericPattern);

                foreach (System.Text.RegularExpressions.Match match in matches)
                {
                    var originalArgs = match.Groups[2].Value;
                    var convertedArgs = ConvertGenericArguments(originalArgs);

                    if (convertedArgs != originalArgs)
                    {
                        var originalMatch = match.Groups[0].Value; // `2<...>
                        var newMatch = match.Groups[1].Value + "<" + convertedArgs + ">";
                        text = text.Replace(originalMatch, newMatch);
                    }
                }

                return text;
            }
            catch
            {
                return text;
            }
        }

        // Convert generic arguments: Stimulsoft.Base.Licenses.StiLicenseProduct -> class Stimulsoft.Base.Licenses.StiLicenseProduct
        private string ConvertGenericArguments(string args)
        {
            try
            {
                var argList = SmartSplitGenericArguments(args);
                var convertedArgs = new List<string>();

                foreach (var arg in argList)
                {
                    var trimmedArg = arg.Trim();

                    // Skip if already has class prefix or is primitive
                    if (trimmedArg.StartsWith("class ") ||
                        trimmedArg.StartsWith("!!") || // Generic parameter like !!0
                        IsPrimitiveType(trimmedArg))
                    {
                        convertedArgs.Add(trimmedArg);
                    }
                    else if (IsCustomType(trimmedArg))
                    {
                        // Add 'class' prefix for custom types
                        convertedArgs.Add("class " + trimmedArg);
                    }
                    else
                    {
                        convertedArgs.Add(trimmedArg);
                    }
                }

                return string.Join(", ", convertedArgs);
            }
            catch
            {
                return args;
            }
        }

        // Check if type is primitive (bool, int32, etc.)
        private bool IsPrimitiveType(string typeName)
        {
            var primitives = new[] { "bool", "int32", "int64", "float32", "float64", "string", "object", "void", "native int" };
            return primitives.Contains(typeName.ToLower());
        }

        // Check if type is custom (not System.* or primitive)
        private bool IsCustomType(string typeName)
        {
            return !typeName.StartsWith("[") && // Not assembly-qualified
                   !typeName.StartsWith("System.") && // Not System type
                   !IsPrimitiveType(typeName) &&
                   typeName.Contains("."); // Has namespace
        }

        // Add method modifiers: .ctor -> instance void, static methods, etc.
        private string AddMethodModifiers(string text)
        {
            // Constructor: void TypeName::.ctor(...) -> instance void class TypeName::.ctor(...)
            if (text.Contains("::.ctor("))
            {
                if (!text.Contains("instance"))
                {
                    text = text.Replace("void ", "instance void class ");
                }
            }

            // CRITICAL: Remove incorrectly placed 'static' keyword
            // Wrong: bool [System.Core]static System.Linq.Enumerable::Any<...>
            // Right: bool [System.Core]System.Linq.Enumerable::Any<...>
            if (text.Contains("]static "))
            {
                text = text.Replace("]static ", "]");
            }

            // CRITICAL: Convert generic method parameters to !!0, !!1 format
            text = ConvertGenericMethodParameters(text);

            return text;
        }

        // KESIN ÇÖZÜM: Generic method parameters'ı !!0, !!1 format'ına çevir
        private string ConvertGenericMethodParameters(string text)
        {
            try
            {
                // Pattern for System.Linq.Enumerable methods
                if (text.Contains("System.Linq.Enumerable::"))
                {
                    // Convert: Any<Stimulsoft.Base.Licenses.StiLicenseProduct>(IEnumerable`1<Stimulsoft.Base.Licenses.StiLicenseProduct>,Func`2<Stimulsoft.Base.Licenses.StiLicenseProduct,bool>)
                    // To:      Any<class Stimulsoft.Base.Licenses.StiLicenseProduct>(class [mscorlib]IEnumerable`1<!!0>, class [mscorlib]Func`2<!!0, bool>)

                    // Extract generic type from method name
                    var genericMethodMatch = System.Text.RegularExpressions.Regex.Match(text, @"::(\w+)<([^>]+)>\(");
                    if (genericMethodMatch.Success)
                    {
                        var methodName = genericMethodMatch.Groups[1].Value;
                        var genericType = genericMethodMatch.Groups[2].Value.Trim();

                        // Replace specific type with !!0 in parameters
                        if (methodName == "Any")
                        {
                            // For Any<T> method: IEnumerable`1<T> -> IEnumerable`1<!!0>, Func`2<T,bool> -> Func`2<!!0,bool>
                            text = text.Replace($"IEnumerable`1<{genericType}>", "IEnumerable`1<!!0>");
                            text = text.Replace($"Func`2<{genericType},bool>", "Func`2<!!0, bool>");
                            text = text.Replace($"Func`2<{genericType}, bool>", "Func`2<!!0, bool>");
                        }
                    }
                }

                return text;
            }
            catch
            {
                return text;
            }
        }

        // dnSpy sistemini kullanarak InstructionVM oluştur
        private object CreateInstructionVMWithDnSpySystem(string instructionText, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[DNSPY-INSTR] Creating instruction: {instructionText}");

                // Parse instruction components
                var (offset, opcodeName, operandText) = ParseILLineComponents(instructionText);
                if (string.IsNullOrEmpty(opcodeName))
                {
                    debugInfo?.AppendLine($"[DNSPY-INSTR] Failed to parse: {instructionText}");
                    return null;
                }

                // Create InstructionVM
                var instructionVMType = FindType("dnSpy.AsmEditor.MethodBody.InstructionVM, dnSpy.AsmEditor");
                if (instructionVMType == null)
                {
                    debugInfo?.AppendLine("[DNSPY-INSTR] InstructionVM type not found");
                    return null;
                }

                var instructionVM = Activator.CreateInstance(instructionVMType);
                if (instructionVM == null) return null;

                // Set OpCode
                OpCode opCode = MapOpcodeToOpCode(opcodeName);
                var codeEnumType = FindType("dnlib.DotNet.Emit.Code");
                object codeEnumValue = null;
                if (codeEnumType != null)
                {
                    codeEnumValue = Enum.Parse(codeEnumType, opCode.Code.ToString());
                    SetProperty(instructionVM, "Code", codeEnumValue);
                }

                // Set Offset
                if (!string.IsNullOrEmpty(offset))
                {
                    uint offsetVal = 0;
                    if (offset.StartsWith("IL_") && uint.TryParse(offset.Substring(3), System.Globalization.NumberStyles.HexNumber, null, out offsetVal))
                        SetProperty(instructionVM, "Offset", offsetVal);
                }

                // CRITICAL: Use dnSpy's operand system
                var operandVM = GetProperty(instructionVM, "InstructionOperandVM");
                if (operandVM != null && !string.IsNullOrEmpty(operandText))
                {
                    SetOperandUsingDnSpySystem(operandVM, codeEnumValue, operandText, debugInfo);
                }

                debugInfo?.AppendLine($"[DNSPY-INSTR] Created instruction: {opcodeName}");
                return instructionVM;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[DNSPY-INSTR] Error: {ex.Message}");
                return null;
            }
        }

        // dnSpy sistemini kullanarak operand set et
        private void SetOperandUsingDnSpySystem(object operandVM, object codeEnumValue, string operandText, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[DNSPY-OPERAND] Setting operand: {operandText}");

                // CRITICAL: Use WriteValue method from dnSpy
                var writeValueMethod = operandVM.GetType().GetMethod("WriteValue");
                if (writeValueMethod != null && codeEnumValue != null)
                {
                    try
                    {
                        // Resolve operand using dnSpy compatible system
                        var resolvedOperand = ResolveOperandAdvanced(GetOpcodeNameFromCode(codeEnumValue), operandText, debugInfo);

                        // Call WriteValue - this automatically sets the correct operand type
                        writeValueMethod.Invoke(operandVM, new object[] { codeEnumValue, resolvedOperand });
                        debugInfo?.AppendLine($"[DNSPY-OPERAND] WriteValue success: {operandText}");
                        return;
                    }
                    catch (Exception writeEx)
                    {
                        debugInfo?.AppendLine($"[DNSPY-OPERAND] WriteValue failed: {writeEx.Message}");
                    }
                }

                // Fallback: Manual operand setting
                debugInfo?.AppendLine("[DNSPY-OPERAND] Using manual fallback");
                SetOperandManually(operandVM, GetOpcodeNameFromCode(codeEnumValue), operandText, debugInfo);
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[DNSPY-OPERAND] Error: {ex.Message}");
            }
        }

        // Code enum'undan opcode name'i al
        private string GetOpcodeNameFromCode(object codeEnumValue)
        {
            try
            {
                if (codeEnumValue != null)
                {
                    var codeValue = codeEnumValue.ToString();
                    // Code enum'dan OpCode'a dönüştür
                    var opCode = MapOpcodeToOpCode(codeValue);
                    return opCode.Name;
                }
                return "nop";
            }
            catch
            {
                return "nop";
            }
        }

        // KESIN ÇÖZÜM: Branch target'ları çözümle
        private void ResolveBranchTargetsForInstruction(object instructionVM, List<object> allInstructions, Dictionary<string, int> branchTargetMap, StringBuilder debugInfo)
        {
            try
            {
                // Get operand VM
                var operandVM = GetProperty(instructionVM, "InstructionOperandVM");
                if (operandVM == null) return;

                // Get operand type
                var operandTypeProp = operandVM.GetType().GetProperty("InstructionOperandType");
                var operandType = operandTypeProp?.GetValue(operandVM);

                if (operandType == null) return;

                var operandTypeStr = operandType.ToString();
                debugInfo?.AppendLine($"[BRANCH-RESOLVE] Checking operand type: {operandTypeStr}");

                // Handle branch targets
                if (operandTypeStr == "BranchTarget")
                {
                    ResolveSingleBranchTarget(operandVM, allInstructions, branchTargetMap, debugInfo);
                }
                else if (operandTypeStr == "SwitchTargets")
                {
                    ResolveSwitchTargets(operandVM, allInstructions, branchTargetMap, debugInfo);
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[BRANCH-RESOLVE] Error: {ex.Message}");
            }
        }

        // Single branch target çözümle (br, brfalse, brtrue etc.)
        private void ResolveSingleBranchTarget(object operandVM, List<object> allInstructions, Dictionary<string, int> branchTargetMap, StringBuilder debugInfo)
        {
            try
            {
                // Get current operand value (should be IL_XXXX string)
                var currentValue = GetProperty(operandVM, "Other");
                if (currentValue == null) return;

                var targetOffset = currentValue.ToString();
                debugInfo?.AppendLine($"[SINGLE-BRANCH] Resolving target: {targetOffset}");

                if (branchTargetMap.TryGetValue(targetOffset, out int targetIndex))
                {
                    if (targetIndex < allInstructions.Count)
                    {
                        var targetInstruction = allInstructions[targetIndex];

                        // Set the target instruction as operand
                        SetProperty(operandVM, "Other", targetInstruction);
                        debugInfo?.AppendLine($"[SINGLE-BRANCH] Resolved {targetOffset} -> instruction {targetIndex}");
                    }
                    else
                    {
                        debugInfo?.AppendLine($"[SINGLE-BRANCH] Target index out of range: {targetIndex}");
                    }
                }
                else
                {
                    debugInfo?.AppendLine($"[SINGLE-BRANCH] Target not found in map: {targetOffset}");
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[SINGLE-BRANCH] Error: {ex.Message}");
            }
        }

        // Switch targets çözümle (switch instruction)
        private void ResolveSwitchTargets(object operandVM, List<object> allInstructions, Dictionary<string, int> branchTargetMap, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine("[SWITCH-TARGETS] Resolving switch targets");

                // Get current operand value (should be array of IL_XXXX strings)
                var currentValue = GetProperty(operandVM, "Other");
                if (currentValue == null) return;

                // Parse switch targets from string format
                var targetsStr = currentValue.ToString();
                if (targetsStr.StartsWith("[") && targetsStr.EndsWith("]"))
                {
                    // Parse: [IL_0001, IL_0005, IL_0010]
                    var targetOffsets = targetsStr.Substring(1, targetsStr.Length - 2)
                        .Split(',')
                        .Select(s => s.Trim())
                        .ToArray();

                    var resolvedTargets = new List<object>();

                    foreach (var targetOffset in targetOffsets)
                    {
                        if (branchTargetMap.TryGetValue(targetOffset, out int targetIndex))
                        {
                            if (targetIndex < allInstructions.Count)
                            {
                                resolvedTargets.Add(allInstructions[targetIndex]);
                                debugInfo?.AppendLine($"[SWITCH-TARGETS] Resolved {targetOffset} -> instruction {targetIndex}");
                            }
                        }
                    }

                    // Set resolved targets as operand
                    SetProperty(operandVM, "Other", resolvedTargets);
                    debugInfo?.AppendLine($"[SWITCH-TARGETS] Resolved {resolvedTargets.Count} switch targets");
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[SWITCH-TARGETS] Error: {ex.Message}");
            }
        }

        // Generic instantiated TypeRef creation for System.Func`2<T1,T2> etc.
        private object CreateGenericInstantiatedTypeRef(string typeName, string genericArgs, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[GENERIC-TYPEREF] Creating instantiated TypeRef for: {typeName} with args: {genericArgs}");
                
                // If genericArgs is provided separately, use it; otherwise parse from typeName
                string baseTypeName = typeName;
                string actualGenericArgs = genericArgs;
                  if (string.IsNullOrEmpty(genericArgs))
                {
                    // Parse: System.Func`2<T1,T2>
                    var match = System.Text.RegularExpressions.Regex.Match(typeName, @"^([^<]+)<(.+)>$");
                    if (match.Success)
                    {
                        baseTypeName = match.Groups[1].Value; // System.Func`2
                        actualGenericArgs = match.Groups[2].Value; // Type1,Type2
                        
                        debugInfo?.AppendLine($"[GENERIC-TYPEREF] Parsed inline - Base: {baseTypeName}, Args: {actualGenericArgs}");
                    }
                }                // Create TypeRef for the base type (e.g., System.Func`2)
                string baseTypeNameWithArity;
                if (baseTypeName.Contains('`'))
                {
                    // Already has arity, use as-is
                    baseTypeNameWithArity = baseTypeName;
                }
                else
                {                    // Calculate arity from generic arguments
                    int arity = SmartSplitGenericArguments(actualGenericArgs).Length;
                    baseTypeNameWithArity = baseTypeName + "`" + arity;
                }
                
                var baseTypeRef2 = CreateTypeRef(baseTypeNameWithArity, module, debugInfo);
                if (baseTypeRef2 == null)
                {
                    debugInfo?.AppendLine($"[GENERIC-TYPEREF] Failed to create base TypeRef for: {baseTypeNameWithArity}");
                    return null;
                }// Parse and create generic arguments
                var genericTypes = ParseGenericTypeArguments(actualGenericArgs, module, debugInfo);
                if (genericTypes == null)
                {
                    debugInfo?.AppendLine($"[GENERIC-TYPEREF] Failed to parse generic arguments: {actualGenericArgs}");
                    return baseTypeRef2;
                }                // Create GenericInstSig
                var genericInstSigType = FindType("dnlib.DotNet.GenericInstSig");
                if (genericInstSigType == null)
                {
                    debugInfo?.AppendLine("[GENERIC-TYPEREF] GenericInstSig type not found");
                    return baseTypeRef2;
                }                try
                {
                    // Convert TypeRef to ClassTypeSig for GenericInstSig constructor
                    var classTypeSigType = FindType("dnlib.DotNet.ClassTypeSig");
                    if (classTypeSigType == null)
                    {
                        debugInfo?.AppendLine("[GENERIC-TYPEREF] ClassTypeSig type not found");
                        return baseTypeRef2;
                    }
                    
                    var classTypeSig = Activator.CreateInstance(classTypeSigType, baseTypeRef2);
                    if (classTypeSig == null)
                    {
                        debugInfo?.AppendLine("[GENERIC-TYPEREF] Failed to create ClassTypeSig");
                        return baseTypeRef2;
                    }
                    
                    // dnSpy pattern: GenericInstSig constructor takes ClassOrValueTypeSig
                    var genericInstSig = Activator.CreateInstance(genericInstSigType, classTypeSig);
                    
                    // Add generic arguments via GenericArguments property
                    var genericArgsProperty = genericInstSigType.GetProperty("GenericArguments");
                    if (genericArgsProperty != null)
                    {
                        var genericArgsList = genericArgsProperty.GetValue(genericInstSig);
                        var addMethod = genericArgsList?.GetType().GetMethod("Add");
                        
                        foreach (var arg in genericTypes)
                        {
                            addMethod?.Invoke(genericArgsList, new[] { arg });
                        }
                    }
                    
                    debugInfo?.AppendLine($"[GENERIC-TYPEREF] Successfully created GenericInstSig for: {typeName} with {genericTypes.Count} args");
                    return genericInstSig;
                }
                catch (Exception ex)
                {
                    debugInfo?.AppendLine($"[GENERIC-TYPEREF] GenericInstSig creation failed: {ex.Message}");
                    return baseTypeRef2;
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[GENERIC-TYPEREF-ERROR] {ex.Message}");
                return null;
            }
        }        // Create TypeRef for external types
        private object CreateTypeRef(string typeName, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                // Clean up double backticks and incorrect arity (e.g., System.Func`2`11 -> System.Func`2)
                if (typeName.Contains("`"))
                {
                    var firstBacktick = typeName.IndexOf('`');
                    var secondBacktick = typeName.IndexOf('`', firstBacktick + 1);
                    if (secondBacktick > 0)
                    {
                        // Remove everything after the second backtick
                        typeName = typeName.Substring(0, secondBacktick);
                        debugInfo?.AppendLine($"[TYPEREF] Cleaned double backtick: {typeName}");
                    }
                }
                
                debugInfo?.AppendLine($"[TYPEREF] Creating TypeRef for: {typeName}");

                // Handle System types through corlib
                if (typeName.StartsWith("System."))
                {
                    var importer = CreateImporter(module);
                    if (importer != null)
                    {
                        try
                        {
                            // Try multiple assembly references for System types
                            var systemType = Type.GetType(typeName);
                            if (systemType == null)
                            {
                                // Try with different assembly qualifiers
                                var assemblyNames = new[] { 
                                    "mscorlib", 
                                    "System.Core", 
                                    "System.Linq", 
                                    "System",
                                    "netstandard"
                                };
                                
                                foreach (var asmName in assemblyNames)
                                {
                                    try
                                    {
                                        systemType = Type.GetType($"{typeName}, {asmName}");
                                        if (systemType != null) break;
                                    }
                                    catch { }
                                }
                            }
                            
                            if (systemType != null)
                            {
                                var importMethod = importer.GetType().GetMethod("Import", new[] { typeof(Type) });
                                if (importMethod != null)
                                {
                                    var imported = importMethod.Invoke(importer, new[] { systemType });
                                    debugInfo?.AppendLine($"[TYPEREF] Successfully imported System type: {typeName}");
                                    return imported;
                                }
                            }
                            else
                            {
                                debugInfo?.AppendLine($"[TYPEREF] System type not found in runtime: {typeName}");
                            }
                        }
                        catch (Exception ex)
                        {
                            debugInfo?.AppendLine($"[TYPEREF] System type import failed: {ex.Message}");
                            // Fall through to manual creation
                        }
                    }
                }                // Manual TypeRef creation - use TypeRefUser (concrete implementation)
                var typeRefType = FindType("dnlib.DotNet.TypeRefUser");
                if (typeRefType == null)
                {
                    // Fallback to TypeRef type for reflection
                    typeRefType = FindType("dnlib.DotNet.TypeRef");
                    if (typeRefType == null)
                    {
                        debugInfo?.AppendLine("[TYPEREF] TypeRef/TypeRefUser type not found");
                        return null;
                    }
                }

                // Parse namespace and name
                var lastDot = typeName.LastIndexOf('.');
                string namespaceName = lastDot > 0 ? typeName.Substring(0, lastDot) : "";
                string name = lastDot > 0 ? typeName.Substring(lastDot + 1) : typeName;

                // Try to find appropriate assembly reference
                object resolutionScope = null;
                
                // For System types, try to find mscorlib or System.Core reference
                if (typeName.StartsWith("System."))
                {
                    var assemblyRefsProperty = module.GetType().GetProperty("AssemblyRefs");
                    if (assemblyRefsProperty != null)
                    {
                        var assemblyRefs = assemblyRefsProperty.GetValue(module) as System.Collections.IList;
                        if (assemblyRefs != null)
                        {
                            foreach (var asmRef in assemblyRefs)
                            {
                                var asmName = GetProperty(asmRef, "Name")?.ToString();
                                if (asmName == "mscorlib" || asmName == "System.Core" || asmName == "System" || asmName == "netstandard")
                                {
                                    resolutionScope = asmRef;
                                    debugInfo?.AppendLine($"[TYPEREF] Using assembly reference: {asmName}");
                                    break;
                                }
                            }
                        }
                    }
                }
                
                // Create TypeRef with proper constructor
                try
                {
                    var constructors = typeRefType.GetConstructors();
                    object typeRef = null;
                    
                    foreach (var ctor in constructors)
                    {
                        var parameters = ctor.GetParameters();                        if (parameters.Length == 3) // ResolutionScope, namespace, name
                        {
                            try
                            {
                                // Convert strings to UTF8String for dnlib
                                var utf8Namespace = ConvertToUTF8String(namespaceName);
                                var utf8Name = ConvertToUTF8String(name);
                                
                                typeRef = ctor.Invoke(new object[] { resolutionScope, utf8Namespace, utf8Name });
                                debugInfo?.AppendLine($"[TYPEREF] Created TypeRef with 3-param constructor for: {typeName}");
                                return typeRef;
                            }
                            catch (Exception ex)
                            {
                                debugInfo?.AppendLine($"[TYPEREF] 3-param constructor failed: {ex.Message}");
                            }
                        }
                    }
                      if (typeRef == null)
                    {
                        // Try different TypeRefUser constructors
                        debugInfo?.AppendLine("[TYPEREF] Trying different TypeRefUser constructors");
                        
                        var constructorTypes = new[]
                        {
                            new[] { typeof(object), typeof(string), typeof(string) }, // resolutionScope, namespace, name
                            new[] { typeof(object), typeof(string) }, // resolutionScope, name
                            new[] { typeof(string), typeof(string) }, // namespace, name
                            new[] { typeof(string) } // name only
                        };
                        
                        foreach (var ctorTypes in constructorTypes)
                        {
                            try
                            {
                                var ctor = typeRefType.GetConstructor(ctorTypes);
                                if (ctor != null)
                                {
                                    object[] args = null;
                                    if (ctorTypes.Length == 3)
                                        args = new object[] { resolutionScope, ConvertToUTF8String(namespaceName), ConvertToUTF8String(name) };
                                    else if (ctorTypes.Length == 2 && ctorTypes[0] == typeof(object))
                                        args = new object[] { resolutionScope, ConvertToUTF8String(typeName) };
                                    else if (ctorTypes.Length == 2)
                                        args = new object[] { ConvertToUTF8String(namespaceName), ConvertToUTF8String(name) };
                                    else if (ctorTypes.Length == 1)
                                        args = new object[] { ConvertToUTF8String(typeName) };
                                    
                                    typeRef = ctor.Invoke(args);
                                    if (typeRef != null)
                                    {
                                        debugInfo?.AppendLine($"[TYPEREF] Created TypeRefUser with {ctorTypes.Length}-param constructor for: {typeName}");
                                        break;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                debugInfo?.AppendLine($"[TYPEREF] {ctorTypes.Length}-param constructor failed: {ex.Message}");
                            }
                        }
                    }
                    
                    return typeRef;
                }
                catch (Exception ex)
                {
                    debugInfo?.AppendLine($"[TYPEREF] Manual creation failed: {ex.Message}");
                }

                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[TYPEREF-ERROR] CreateTypeRef({typeName}): {ex.Message}");
                return null;
            }
        }

        // Create dnlib Importer
        private object CreateImporter(dnlib.DotNet.ModuleDef module)
        {
            try
            {
                var importerType = FindType("dnlib.DotNet.Importer");
                if (importerType == null) return null;

                return Activator.CreateInstance(importerType, module);
            }
            catch
            {
                return null;
            }
        }

        // Create TypeSig from string representation
        private object CreateTypeSigFromString(string typeName, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[TYPESIG] Creating TypeSig for: {typeName}");

                // Handle primitive types
                var corLibTypeSig = CreateCorLibTypeSig(typeName, module);
                if (corLibTypeSig != null)
                {
                    debugInfo?.AppendLine($"[TYPESIG] Created CorLibTypeSig for: {typeName}");
                    return corLibTypeSig;
                }

                // Handle generic types: List<T>, Func<T1,T2>, etc.
                if (typeName.Contains('<') && typeName.Contains('>'))
                {
                    var genericTypeSig = CreateGenericTypeSig(typeName, module, debugInfo);
                    if (genericTypeSig != null)
                    {
                        debugInfo?.AppendLine($"[TYPESIG] Created GenericTypeSig for: {typeName}");
                        return genericTypeSig;
                    }
                }

                // Handle class/reference types
                var typeRef = CreateTypeRef(typeName, module, debugInfo);
                if (typeRef != null)
                {
                    var classTypeSig = CreateClassTypeSig(typeRef);
                    if (classTypeSig != null)
                    {
                        debugInfo?.AppendLine($"[TYPESIG] Created ClassTypeSig for: {typeName}");
                        return classTypeSig;
                    }
                }

                debugInfo?.AppendLine($"[TYPESIG] Unable to resolve type: {typeName}");
                return null;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[TYPESIG-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create CorLibTypeSig for primitive types
        private object CreateCorLibTypeSig(string elementTypeName, dnlib.DotNet.ModuleDef module)
        {
            try
            {
                var corLibTypes = GetProperty(module, "CorLibTypes");
                if (corLibTypes == null) return null;

                var elementTypeProperty = corLibTypes.GetType().GetProperty(elementTypeName);
                return elementTypeProperty?.GetValue(corLibTypes);
            }
            catch
            {
                return null;
            }
        }

        // Create generic TypeSig for generic types like List<T>
        private object CreateGenericTypeSig(string typeName, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[GENERIC-TYPESIG] Processing: {typeName}");

                // Parse: List<T> or Func<T1,T2>
                var match = System.Text.RegularExpressions.Regex.Match(typeName, @"^([^<]+)<(.+)>$");
                if (!match.Success) return null;                var baseTypeName = match.Groups[1].Value.Trim();
                var genericArgs = match.Groups[2].Value.Trim();                // Use smart split to count generic arguments correctly
                var smartArgs = SmartSplitGenericArguments(genericArgs);
                debugInfo?.AppendLine($"[GENERIC-TYPESIG] Base: {baseTypeName}, Count: {smartArgs.Length}, Args: {genericArgs}");

                // Create TypeRef for base generic type (e.g., List`1)
                // Check if baseTypeName already has generic count (e.g., "System.Func`2")
                string fullBaseTypeName = baseTypeName;
                if (!baseTypeName.Contains("`"))
                {
                    fullBaseTypeName = baseTypeName + "`" + smartArgs.Length;
                }
                
                var baseTypeRef = CreateTypeRef(fullBaseTypeName, module, debugInfo);
                if (baseTypeRef == null) return null;

                // Parse generic arguments
                var args = ParseGenericTypeArguments(genericArgs, module, debugInfo);
                if (args == null) return null;

                // Create GenericInstSig
                var genericInstSigType = FindType("dnlib.DotNet.GenericInstSig");
                if (genericInstSigType == null)
                {
                    debugInfo?.AppendLine("[GENERIC-TYPESIG] GenericInstSig type not found");
                    return baseTypeRef;
                }                try
                {
                    // Convert baseTypeRef to ClassOrValueTypeSig for GenericInstSig constructor
                    object classSig = null;
                    var classSigType = FindType("dnlib.DotNet.ClassSig");
                    var valueTypeSigType = FindType("dnlib.DotNet.ValueTypeSig");
                    
                    if (classSigType != null)
                    {
                        // For most reference types, use ClassSig
                        classSig = Activator.CreateInstance(classSigType, baseTypeRef);
                    }
                    
                    if (classSig == null)
                    {
                        debugInfo?.AppendLine("[GENERIC-TYPESIG] Failed to create ClassOrValueTypeSig");
                        return baseTypeRef;
                    }
                    
                    // dnSpy pattern: GenericInstSig constructor only takes ClassOrValueTypeSig
                    var genericInstSig = Activator.CreateInstance(genericInstSigType, classSig);
                    
                    // Add generic arguments via GenericArguments property
                    var genericArgsProperty = genericInstSigType.GetProperty("GenericArguments");
                    if (genericArgsProperty != null)
                    {
                        var genericArgsList = genericArgsProperty.GetValue(genericInstSig);
                        var addMethod = genericArgsList?.GetType().GetMethod("Add");
                        
                        foreach (var arg in args)
                        {
                            addMethod?.Invoke(genericArgsList, new[] { arg });
                        }
                    }
                    
                    debugInfo?.AppendLine($"[GENERIC-TYPESIG] Successfully created GenericInstSig for: {typeName} with {args.Count} args");
                    return genericInstSig;
                }
                catch (Exception ex)
                {
                    debugInfo?.AppendLine($"[GENERIC-TYPESIG] GenericInstSig creation failed: {ex.Message}");
                    return baseTypeRef;
                }
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[GENERIC-TYPESIG-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create ClassTypeSig from TypeDefOrRef
        private object CreateClassTypeSig(object typeDefOrRef)
        {
            try
            {
                var classTypeSigType = FindType("dnlib.DotNet.ClassSig");
                if (classTypeSigType == null)
                {
                    classTypeSigType = FindType("dnlib.DotNet.ClassTypeSig");
                }

                if (classTypeSigType != null)
                {
                    return Activator.CreateInstance(classTypeSigType, typeDefOrRef);
                }

                return null;            }
            catch
            {
                return null;
            }
        }

        // Create MemberRef for constructor
        private object CreateMemberRefForConstructor(object typeRef, string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[MEMBERREF-CTOR] Creating MemberRef for constructor: {typeRef}::.ctor({parameters})");
                
                var memberRefType = FindType("dnlib.DotNet.MemberRef");
                if (memberRefType == null)
                {
                    debugInfo?.AppendLine("[MEMBERREF-CTOR] MemberRef type not found");
                    return null;
                }

                // Create constructor signature
                var methodSig = CreateConstructorSigFromParameters(parameters, module, debugInfo);
                if (methodSig == null) return null;

                // Create MemberRef
                var memberRef = Activator.CreateInstance(memberRefType);
                if (memberRef == null) return null;

                // Set properties
                var classProperty = memberRefType.GetProperty("Class");
                var nameProperty = memberRefType.GetProperty("Name");
                var signatureProperty = memberRefType.GetProperty("Signature");

                classProperty?.SetValue(memberRef, typeRef);
                nameProperty?.SetValue(memberRef, ".ctor");
                signatureProperty?.SetValue(memberRef, methodSig);

                debugInfo?.AppendLine("[MEMBERREF-CTOR] Successfully created constructor MemberRef");
                return memberRef;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[MEMBERREF-CTOR-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create MemberRef for generic method
        private object CreateMemberRefForGenericMethod(string typeName, string methodName, string genericArgs, string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[MEMBERREF-GENERIC] Creating MemberRef for generic method: {typeName}::{methodName}<{genericArgs}>({parameters})");
                
                var typeRef = CreateTypeRef(typeName, module, debugInfo);
                if (typeRef == null) return null;

                var memberRef = CreateMemberRef(typeName, methodName, parameters, module, debugInfo);
                if (memberRef == null) return null;

                // Create MethodSpec for generic instantiation
                var methodSpecType = FindType("dnlib.DotNet.MethodSpec");
                var genericInstMethodSigType = FindType("dnlib.DotNet.GenericInstMethodSig");
                
                if (methodSpecType == null || genericInstMethodSigType == null)
                {
                    debugInfo?.AppendLine("[MEMBERREF-GENERIC] MethodSpec or GenericInstMethodSig type not found");
                    return memberRef; // Return base MemberRef
                }

                // Parse generic arguments
                var args = ParseGenericTypeArguments(genericArgs, module, debugInfo);
                if (args == null) return memberRef;

                // Create GenericInstMethodSig
                var genericInstMethodSig = Activator.CreateInstance(genericInstMethodSigType, args);

                // Create MethodSpec
                var methodSpec = Activator.CreateInstance(methodSpecType);
                var methodProperty = methodSpecType.GetProperty("Method");
                var instantiationProperty = methodSpecType.GetProperty("GenericInstMethodSig");

                // Set MethodSpec properties
                methodProperty?.SetValue(methodSpec, memberRef);
                instantiationProperty?.SetValue(methodSpec, genericInstMethodSig);

                debugInfo?.AppendLine($"[MEMBERREF-GENERIC] Successfully created MethodSpec with MemberRef for: {typeName}::{methodName}<{genericArgs}>");
                return methodSpec;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[MEMBERREF-GENERIC-ERROR] {ex.Message}");
                return null;
            }
        }

        // Create basic MemberRef
        private object CreateMemberRef(string typeName, string methodName, string parameters, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                debugInfo?.AppendLine($"[MEMBERREF] Creating MemberRef for: {typeName}::{methodName}({parameters})");
                
                var memberRefType = FindType("dnlib.DotNet.MemberRef");
                if (memberRefType == null)
                {
                    debugInfo?.AppendLine("[MEMBERREF] MemberRef type not found");
                    return null;
                }

                // Create TypeRef for declaring type
                var typeRef = CreateTypeRef(typeName, module, debugInfo);
                if (typeRef == null)
                {
                    debugInfo?.AppendLine($"[MEMBERREF] Failed to create TypeRef for: {typeName}");
                    return null;
                }

                // Create method signature
                var methodSig = CreateMethodSigFromParameters(parameters, module, debugInfo);
                if (methodSig == null) return null;

                // Create MemberRef
                var memberRef = Activator.CreateInstance(memberRefType);
                if (memberRef == null) return null;

                // Set properties
                var classProperty = memberRefType.GetProperty("Class");
                var nameProperty = memberRefType.GetProperty("Name");
                var signatureProperty = memberRefType.GetProperty("Signature");

                classProperty?.SetValue(memberRef, typeRef);
                nameProperty?.SetValue(memberRef, methodName);
                signatureProperty?.SetValue(memberRef, methodSig);

                debugInfo?.AppendLine("[MEMBERREF] Successfully created MemberRef");
                return memberRef;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[MEMBERREF-ERROR] {ex.Message}");                return null;
            }
        }

        // Parse generic type arguments from string
        private System.Collections.IList ParseGenericTypeArguments(string genericArgs, dnlib.DotNet.ModuleDef module, StringBuilder debugInfo)
        {
            try
            {
                var typeSigListType = FindType("System.Collections.Generic.List`1").MakeGenericType(FindType("dnlib.DotNet.TypeSig"));
                var typeList = Activator.CreateInstance(typeSigListType);
                var addMethod = typeSigListType.GetMethod("Add");

                // Smart split: handle nested generics properly
                var args = SmartSplitGenericArguments(genericArgs);
                debugInfo?.AppendLine($"[GENERIC-PARSE] Parsing {args.Length} generic arguments: {string.Join(", ", args)}");

                foreach (var arg in args)
                {
                    var typeSig = CreateTypeSigFromString(arg, module, debugInfo);
                    if (typeSig != null)
                    {
                        addMethod?.Invoke(typeList, new[] { typeSig });
                        debugInfo?.AppendLine($"[GENERIC-PARSE] Added type: {arg}");
                    }
                }

                return typeList as System.Collections.IList;
            }
            catch (Exception ex)
            {
                debugInfo?.AppendLine($"[GENERIC-PARSE-ERROR] {ex.Message}");
                return null;
            }
        }

        // Smart split generic arguments respecting nested generics
        private string[] SmartSplitGenericArguments(string genericArgs)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            int depth = 0;
            
            for (int i = 0; i < genericArgs.Length; i++)
            {
                char c = genericArgs[i];
                
                if (c == '<')
                {
                    depth++;
                    current.Append(c);
                }
                else if (c == '>')
                {
                    depth--;
                    current.Append(c);
                }
                else if (c == ',' && depth == 0)
                {
                    // Only split on comma at top level
                    var arg = current.ToString().Trim();
                    if (!string.IsNullOrEmpty(arg))
                    {
                        result.Add(arg);
                    }
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }
            
            // Add the last argument
            var lastArg = current.ToString().Trim();
            if (!string.IsNullOrEmpty(lastArg))
            {
                result.Add(lastArg);
            }
            
            return result.ToArray();
        }

        // Convert string to UTF8String for dnlib
        private object ConvertToUTF8String(string value)
        {
            try
            {
                var utf8StringType = FindType("dnlib.DotNet.UTF8String");
                if (utf8StringType == null) return value; // Fallback to string
                
                // Try implicit conversion operator
                var implicitMethod = utf8StringType.GetMethod("op_Implicit", new[] { typeof(string) });
                if (implicitMethod != null)
                {
                    return implicitMethod.Invoke(null, new object[] { value });
                }
                
                // Try constructor
                var constructor = utf8StringType.GetConstructor(new[] { typeof(string) });
                if (constructor != null)
                {
                    return constructor.Invoke(new object[] { value });
                }
                
                return value; // Fallback
            }
            catch
            {
                return value; // Fallback to string
            }
        }
    }
}