name: GitHub CI
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
  release:
    types:
      - released
  workflow_dispatch:

jobs:
  build:
    name: Build
    runs-on: windows-latest
    defaults:
        run:
          shell: pwsh
    strategy:
      matrix:
        platform: [netframework, net-x86, net-x64]
        include:
          - platform: netframework
            package-name: netframework
            build-dir: net48
          - platform: net-x86
            package-name: net-win32
            build-dir: net8.0-windows\win-x86\publish
          - platform: net-x64
            package-name: net-win64
            build-dir: net8.0-windows\win-x64\publish

    steps:
      - uses: actions/checkout@v4.2.2
        with:
          submodules: true

      - uses: actions/setup-dotnet@v4.3.1
        with:
          dotnet-version: 8.0.x
          dotnet-quality: ga

      - uses: microsoft/setup-msbuild@v2.0.0

      - name: Build dnSpy (${{matrix.platform}})
        run: .\build.ps1 ${{matrix.platform}}

      - name: Create output directory
        run: New-Item -ItemType Directory -Path C:\builtfiles\dnSpy-${{matrix.platform}} -Force > $null
        continue-on-error: true

      - name:  Copy release files for upload
        run: Copy-Item -Path dnSpy\dnSpy\bin\Release\${{matrix.build-dir}}\* -Destination C:\builtfiles\dnSpy-${{matrix.platform}} -Recurse

      - uses: actions/upload-artifact@v4.6.2
        if: github.ref == 'refs/heads/master' || startsWith(github.ref, 'refs/tags/')
        with:
          name: dnSpy-${{matrix.package-name}}
          path: C:\builtfiles\dnSpy-${{matrix.platform}}
          if-no-files-found: error
          compression-level: 9
