# DnSpyAI Kurulum Betiği

# dnSpy yolunu belirle
$dnSpyPath = ""

# Kullanıcıdan dnSpy yolunu al
while ([string]::IsNullOrEmpty($dnSpyPath) -or -not (Test-Path $dnSpyPath)) {
    $dnSpyPath = Read-Host "Lütfen dnSpy'ın kurulu olduğu dizini girin (örn: C:\Program Files\dnSpy)"
    
    if (-not (Test-Path $dnSpyPath)) {
        Write-Host "Belirtilen dizin bulunamadı. Lütfen geçerli bir dizin girin." -ForegroundColor Red
    }
}

# Extensions klasörünü kontrol et ve gerekirse oluştur
$extensionsPath = Join-Path -Path $dnSpyPath -ChildPath "Extensions"
if (-not (Test-Path $extensionsPath)) {
    Write-Host "Extensions klasörü bulunamadı. Oluşturuluyor..." -ForegroundColor Yellow
    New-Item -Path $extensionsPath -ItemType Directory | Out-Null
}

# DnSpyAI.dll dosyasını kopyala
$dllPath = Join-Path -Path $PSScriptRoot -ChildPath "DnSpyAI.dll"
$targetPath = Join-Path -Path $extensionsPath -ChildPath "DnSpyAI.dll"

try {
    Copy-Item -Path $dllPath -Destination $targetPath -Force
    Write-Host "DnSpyAI.dll başarıyla kopyalandı." -ForegroundColor Green
}
catch {
    Write-Host "Dosya kopyalanırken hata oluştu: $_" -ForegroundColor Red
    exit 1
}

# API anahtarını ayarla
$apiKey = Read-Host "OpenAI API anahtarınızı girin (isteğe bağlı, daha sonra da ayarlayabilirsiniz)"
if (-not [string]::IsNullOrEmpty($apiKey)) {
    try {
        [System.Environment]::SetEnvironmentVariable("OPENAI_API_KEY", $apiKey, [System.EnvironmentVariableTarget]::User)
        Write-Host "API anahtarı başarıyla ayarlandı." -ForegroundColor Green
    }
    catch {
        Write-Host "API anahtarı ayarlanırken hata oluştu: $_" -ForegroundColor Yellow
        Write-Host "API anahtarını daha sonra manuel olarak ayarlayabilirsiniz." -ForegroundColor Yellow
    }
}

Write-Host "`nKurulum tamamlandı!" -ForegroundColor Green
Write-Host "DnSpyAI eklentisini kullanmak için dnSpy'ı yeniden başlatın." -ForegroundColor Cyan
Write-Host "Daha fazla bilgi için README.md ve KURULUM.md dosyalarına bakın." -ForegroundColor Cyan

Read-Host "Çıkmak için Enter tuşuna basın"
