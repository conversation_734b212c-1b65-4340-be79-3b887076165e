{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\dnspyai-son-4\\DnSpyAI\\DnSpyAI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\dnspyai-son-4\\DnSpyAI\\DnSpyAI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\dnspyai-son-4\\DnSpyAI\\DnSpyAI.csproj", "projectName": "DnSpyAI.x", "projectPath": "C:\\Users\\<USER>\\Desktop\\dnspyai-son-4\\DnSpyAI\\DnSpyAI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\dnspyai-son-4\\DnSpyAI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"D:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages": {}, "D:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"noWarn": ["NU1603"], "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AvalonEdit": {"target": "Package", "version": "[6.3.0.90, )"}, "Google_GenerativeAI": {"target": "Package", "version": "[2.5.5, )"}, "Microsoft.VisualStudio.Language": {"target": "Package", "version": "[17.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}