<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<ctrls:WindowBase x:Class="dnSpy.Documents.Tabs.Dialogs.TabsDlg"
        x:ClassModifier="internal"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:dnSpy.Documents.Tabs.Dialogs"
        xmlns:ctrls="clr-namespace:dnSpy.Contracts.Controls;assembly=dnSpy.Contracts.DnSpy"
        xmlns:mvvm="clr-namespace:dnSpy.Contracts.MVVM;assembly=dnSpy.Contracts.DnSpy"
        xmlns:p="clr-namespace:dnSpy.Properties"
        Style="{StaticResource DialogWindowStyle}" WindowStartupLocation="CenterOwner"
        Width="1000" Height="400"
        MinWidth="500" MinHeight="300"
        Title="{x:Static p:dnSpy_Resources.TabsDlg_Title}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <ListView Name="listView"
                  Grid.Row="0"
                  Margin="5 5 5 0"
                  MouseDoubleClick="ListView_MouseDoubleClick"
                  VirtualizingStackPanel.IsVirtualizing="True"
                  VirtualizingStackPanel.VirtualizationMode="Recycling"
                  mvvm:InitDataTemplateAP.Initialize="True"
                  mvvm:AutomationPeerMemoryLeakWorkaround.Initialize="True"
                  SelectionMode="Extended"
                  ItemsSource="{Binding Collection}"
                  SelectedItem="{Binding SelectedItem}">
            <ListView.Resources>
                <local:TabColumnConverter x:Key="tabColumnConverter" />
            </ListView.Resources>
            <ListView.View>
                <GridView AllowsColumnReorder="True">
                    <GridViewColumn Header="{x:Static p:dnSpy_Resources.TabsDlg_Name_Column}" Width="200">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ContentPresenter Content="{Binding NameObject, Mode=OneWay, Converter={StaticResource tabColumnConverter}, ConverterParameter=Name}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="{x:Static p:dnSpy_Resources.TabsDlg_Module_Column}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ContentPresenter Content="{Binding ModuleObject, Mode=OneWay, Converter={StaticResource tabColumnConverter}, ConverterParameter=Module}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="{x:Static p:dnSpy_Resources.TabsDlg_Path_Column}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ContentPresenter Content="{Binding PathObject, Mode=OneWay, Converter={StaticResource tabColumnConverter}, ConverterParameter=Path}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <Button Grid.Column="1" Content="{x:Static p:dnSpy_Resources.TabsDlg_Activate_Button}" Style="{StaticResource DialogButton}" Margin="0 0 5 0" Click="activateButton_Click" IsDefault="True" />
            <Button Grid.Column="2" Content="{Binding SaveText}" Style="{StaticResource DialogButton}" Margin="0 0 5 0" Command="{Binding SaveCommand}" />
            <Button Grid.Column="3" Content="{x:Static p:dnSpy_Resources.TabsDlg_CloseWindow_Button}" Style="{StaticResource DialogButton}" Margin="0 0 0 0" Command="{Binding CloseTabCommand}" />
        </Grid>
    </Grid>
</ctrls:WindowBase>
