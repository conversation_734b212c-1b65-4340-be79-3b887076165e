<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<UserControl x:Class="dnSpy.Documents.Tabs.DocViewer.WaitAdorner"
             x:ClassModifier="internal"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:p="clr-namespace:dnSpy.Properties">
    <Border
        Background="{DynamicResource DecompilerTextViewWaitAdornerBackground}"
        TextBlock.Foreground="{DynamicResource DecompilerTextViewWaitAdornerForeground}">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Name="textBlock" FontSize="14pt" HorizontalAlignment="Center" />
            <ProgressBar Name="progressBar" IsIndeterminate="True" Height="16" MinWidth="200" Margin="0 4" HorizontalAlignment="Center" />
            <Button Name="button" Style="{StaticResource DialogButton}" Click="button_Click" HorizontalAlignment="Center" Content="{x:Static p:dnSpy_Resources.DecompileCancelButton}"/>
        </StackPanel>
    </Border>
</UserControl>
