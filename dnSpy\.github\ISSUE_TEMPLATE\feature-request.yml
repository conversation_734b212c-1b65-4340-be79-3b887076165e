name: Feature Request
description: Suggest a new feature
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Before clicking `Submit new issue`, try the latest build [![](https://github.com/dnSpyEx/dnSpy/workflows/GitHub%20CI/badge.svg)](https://github.com/dnSpyEx/dnSpy/actions?query=branch%3Amaster)
  - type: textarea
    id: description
    attributes:
      label: Problem Description
      description: A clear and concise description of what the problem is that your proposal is trying to solve.
    validations:
      required: true
  - type: textarea
    id: proposal
    attributes:
      label: Proposal
      description: A clear and concise description of the solution or feature you have envisioned.
    validations:
      required: true
  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives
      description: A clear and concise description of any alternative solutions or features you have considered.
    validations:
      required: false
  - type: textarea
    id: context
    attributes:
      label: Additional Context
      description: Add any other context or screenshots about the feature request here.
    validations:
      required: false
