<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<UserControl x:Class="dnSpy.Hex.Intellisense.HexQuickInfoPresenterControl"
             x:ClassModifier="internal"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:dnSpy.Hex.Intellisense"
             xmlns:mvvm="clr-namespace:dnSpy.Contracts.MVVM;assembly=dnSpy.Contracts.DnSpy"
             xmlns:img="clr-namespace:dnSpy.Contracts.Images;assembly=dnSpy.Contracts.DnSpy">
    <!-- The scrollbars are disabled because it's not focusable and we want the content
         to wrap if it doesn't fit. -->
    <ListBox ItemsSource="{Binding QuickInfoContent}"
             Focusable="False"
             Background="{DynamicResource QuickInfoBackground}"
             Foreground="{DynamicResource QuickInfoForeground}"
             BorderBrush="{DynamicResource QuickInfoBorder}"
             mvvm:AutomationPeerMemoryLeakWorkaround.Initialize="True"
             img:DsImage.BackgroundBrush="{Binding Background, RelativeSource={RelativeSource Self}}"
             SnapsToDevicePixels="True"
             ScrollViewer.HorizontalScrollBarVisibility="Disabled"
             ScrollViewer.VerticalScrollBarVisibility="Disabled"
             Padding="6 1 8 0"
             HorizontalContentAlignment="Left">
        <ListBox.Resources>
            <Style TargetType="{x:Type ListBoxItem}">
                <Setter Property="SnapsToDevicePixels" Value="True" />
                <Setter Property="Padding" Value="4" />
                <Setter Property="Background" Value="Transparent" />
                <Setter Property="BorderBrush" Value="Transparent" />
                <Setter Property="BorderThickness" Value="1" />
                <Setter Property="FocusVisualStyle" Value="{x:Null}" />
                <Setter Property="OverridesDefaultStyle" Value="True" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type ListBoxItem}">
                            <Border x:Name="Bd"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Background="{TemplateBinding Background}"
                                    Padding="{TemplateBinding Padding}"
                                    SnapsToDevicePixels="True">
                                <ContentPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" Focusable="False" Margin="0 3" />
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ListBox.Resources>
        <ListBox.ItemContainerStyle>
            <Style TargetType="{x:Type ListBoxItem}"
                   BasedOn="{StaticResource {x:Type ListBoxItem}}">
                <!-- Prevent the quick info control from getting focus. Ctrl+K, Ctrl+I then click on it and
                     the ListBoxItem gets focus. We don't want that. -->
                <Setter Property="Focusable" Value="False" />
            </Style>
        </ListBox.ItemContainerStyle>
    </ListBox>
</UserControl>
