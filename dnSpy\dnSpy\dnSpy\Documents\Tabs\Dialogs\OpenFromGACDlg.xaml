<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<ctrls:WindowBase x:Class="dnSpy.Documents.Tabs.Dialogs.OpenFromGACDlg"
        x:ClassModifier="internal"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:dnSpy.Documents.Tabs.Dialogs"
        xmlns:ctrls="clr-namespace:dnSpy.Contracts.Controls;assembly=dnSpy.Contracts.DnSpy"
        xmlns:mvvm="clr-namespace:dnSpy.Contracts.MVVM;assembly=dnSpy.Contracts.DnSpy"
        xmlns:p="clr-namespace:dnSpy.Properties"
        Style="{StaticResource DialogWindowStyle}" WindowStartupLocation="CenterOwner"
        MinHeight="200" MinWidth="300"
        Title="{x:Static p:dnSpy_Resources.OpenGAC_Title}" Height="540" Width="620"
        FocusManager.FocusedElement="{Binding ElementName=searchBox}">
    <Grid>
        <Grid.Resources>
            <BooleanToVisibilityConverter x:Key="booleanToVisibilityConverter" />
        </Grid.Resources>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Margin="5 5 5 0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="180" />
            </Grid.ColumnDefinitions>
            <ListView Name="listView"
                      Grid.Column="0"
                      MouseDoubleClick="ListView_MouseDoubleClick"
                      VirtualizingStackPanel.IsVirtualizing="True"
                      VirtualizingStackPanel.VirtualizationMode="Recycling"
                      mvvm:InitDataTemplateAP.Initialize="True"
                      mvvm:AutomationPeerMemoryLeakWorkaround.Initialize="True"
                      SelectionMode="Extended"
                      ItemsSource="{Binding CollectionView}"
                      SelectedItem="{Binding SelectedItem}">
                <ListView.Resources>
                    <local:GACFileColumnConverter x:Key="gacFileColumnConverter" />
                </ListView.Resources>
                <ListView.View>
                    <GridView AllowsColumnReorder="True">
                        <GridViewColumn Header="{x:Static p:dnSpy_Resources.OpenGAC_Name_Column}" Width="320">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding NameObject, Mode=OneWay, Converter={StaticResource gacFileColumnConverter}, ConverterParameter=Name}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                        <GridViewColumn Header="{x:Static p:dnSpy_Resources.OpenGAC_Version_Column}" Width="75">
                            <GridViewColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding VersionObject, Mode=OneWay, Converter={StaticResource gacFileColumnConverter}, ConverterParameter=Version}" />
                                </DataTemplate>
                            </GridViewColumn.CellTemplate>
                        </GridViewColumn>
                    </GridView>
                </ListView.View>
            </ListView>
            <Grid Grid.Column="1" Margin="5 0 0 0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <TextBox Grid.Row="0" Name="searchBox" Text="{Binding SearchText, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" ToolTip="{Binding OpenGAC_Search_ToolTip}" />
                <CheckBox Grid.Row="1" Margin="0 5 0 0" Content="{x:Static p:dnSpy_Resources.OpenGAC_ShowDupes_CheckBox}" IsChecked="{Binding ShowDuplicates}" />
                <TextBlock Grid.Row="2" Margin="10 5 0 0"><Bold><Run Text="{x:Static p:dnSpy_Resources.OpenGAC_Name_Label}"/></Bold></TextBlock>
                <TextBlock Grid.Row="3" Margin="15 1 0 0" Text="{Binding ElementName=listView, Path=SelectedItem.Name}" TextWrapping="Wrap" />
                <TextBlock Grid.Row="4" Margin="10 1 0 0"><Bold><Run Text="{x:Static p:dnSpy_Resources.OpenGAC_CreatedBy_Label}"/></Bold></TextBlock>
                <TextBlock Grid.Row="5" Margin="15 1 0 0" Text="{Binding ElementName=listView, Path=SelectedItem.CreatedBy}" TextWrapping="Wrap" />
                <TextBlock Grid.Row="6" Margin="10 1 0 0"><Bold><Run Text="{x:Static p:dnSpy_Resources.OpenGAC_Version_Label}"/></Bold></TextBlock>
                <TextBlock Grid.Row="7" Margin="15 1 0 0" Text="{Binding ElementName=listView, Path=SelectedItem.Version}" TextWrapping="Wrap" />
                <TextBlock Grid.Row="8" Margin="10 1 0 0"><Bold><Run Text="{x:Static p:dnSpy_Resources.OpenGAC_FileVersion_Label}"/></Bold></TextBlock>
                <TextBlock Grid.Row="9" Margin="15 1 0 0" Text="{Binding ElementName=listView, Path=SelectedItem.FileVersion}" TextWrapping="Wrap" />
                <TextBlock Grid.Row="10" Margin="10 1 0 0"><Bold><Run Text="{x:Static p:dnSpy_Resources.OpenGAC_Path_Label}"/></Bold></TextBlock>
                <TextBlock Grid.Row="11" Margin="15 1 0 0" Text="{Binding ElementName=listView, Path=SelectedItem.Path}" TextWrapping="Wrap" />
            </Grid>
        </Grid>

        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <ProgressBar Name="progressBar" Grid.Column="0" IsIndeterminate="True" Margin="0 0 5 0" Visibility="{Binding SearchingGAC, Converter={StaticResource booleanToVisibilityConverter}}" />
            <TextBlock Grid.Column="0" Text="{Binding FilesShownInfo}" Visibility="{Binding NotSearchingGAC, Converter={StaticResource booleanToVisibilityConverter}}" />
            <Button Grid.Column="1" Content="{x:Static p:dnSpy_Resources.Button_OK}" Style="{StaticResource DialogButton}" Margin="0 0 5 0" IsDefault="True" Click="okButton_Click" />
            <Button Grid.Column="2" Content="{x:Static p:dnSpy_Resources.Button_Cancel}" Style="{StaticResource DialogButton}" Margin="0 0 0 0" IsCancel="True" />
        </Grid>
    </Grid>
</ctrls:WindowBase>
