<!--
    Copyright (C) 2014-2019 <EMAIL>

    This file is part of dnSpy

    dnSpy is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    dnSpy is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with dnSpy.  If not, see <http://www.gnu.org/licenses/>.
-->
<UserControl x:Class="dnSpy.Bookmarks.ToolWindows.Bookmarks.BookmarksControl"
             x:ClassModifier="internal"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:dnSpy.Bookmarks.ToolWindows.Bookmarks"
             xmlns:ctrls="clr-namespace:dnSpy.Contracts.Controls.ToolWindows;assembly=dnSpy.Contracts.DnSpy"
             xmlns:mvvm="clr-namespace:dnSpy.Contracts.MVVM;assembly=dnSpy.Contracts.DnSpy"
             xmlns:img="clr-namespace:dnSpy.Contracts.Images;assembly=dnSpy.Contracts.DnSpy"
             xmlns:ui="clr-namespace:dnSpy.Contracts.Controls.ToolWindows;assembly=dnSpy.Contracts.DnSpy"
             xmlns:p="clr-namespace:dnSpy.Properties"
             Name="thisControl">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>
        <Grid Grid.Row="0" Background="{DynamicResource EnvironmentCommandBar}" mvvm:InitDataTemplateAP.Initialize="True" img:DsImage.BackgroundBrush="{DynamicResource EnvironmentCommandBarIcon}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Margin="0 3" Orientation="Horizontal">
                <StackPanel.Resources>
                    <Style TargetType="{x:Type img:DsImage}" BasedOn="{StaticResource {x:Type img:DsImage}}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType={x:Type Control}, AncestorLevel=1}, Path=IsEnabled}" Value="False">
                                <Setter Property="Opacity" Value="0.30" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </StackPanel.Resources>
                <Button Margin="5 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding ToggleCreateBookmarkCommand}" ToolTip="{Binding ToggleCreateBookmarkToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.Bookmark}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToPreviousBookmarkCommand}" ToolTip="{Binding GoToPreviousBookmarkToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.PreviousBookmark}"/>
                </Button>
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToNextBookmarkCommand}" ToolTip="{Binding GoToNextBookmarkToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.NextBookmark}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToPreviousBookmarkSameLabelCommand}" ToolTip="{Binding GoToPreviousBookmarkSameLabelToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.PreviousBookmarkInFolder}"/>
                </Button>
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToNextBookmarkSameLabelCommand}" ToolTip="{Binding GoToNextBookmarkSameLabelToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.NextBookmarkInFolder}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToPreviousBookmarkInFileCommand}" ToolTip="{Binding GoToPreviousBookmarkInFileToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.PreviousBookmarkInFile}"/>
                </Button>
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToNextBookmarkInFileCommand}" ToolTip="{Binding GoToNextBookmarkInFileToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.NextBookmarkInFile}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding RemoveBookmarksCommand}" ToolTip="{Binding RemoveBookmarkToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.Cancel}"/>
                </Button>
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding RemoveMatchingBookmarksCommand}" ToolTip="{Binding RemoveMatchingBookmarksToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.ClearBookmark}"/>
                </Button>
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding ToggleMatchingBookmarksCommand}" ToolTip="{Binding ToggleMatchingBookmarksToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.BookmarkDisabled}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding ExportMatchingBookmarksCommand}" ToolTip="{Binding ExportMatchingBookmarksToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.Open}"/>
                </Button>
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding ImportBookmarksCommand}" ToolTip="{Binding ImportBookmarksToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.Import}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding GoToLocationCommand}" ToolTip="{Binding GoToLocationToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.GoToSourceCode}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding ResetSearchSettingsCommand}" ToolTip="{Binding ResetSearchSettingsToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.ClearWindowContent}"/>
                </Button>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
            </StackPanel>
            <Label Grid.Column="1" Margin="0 0 0 0" VerticalAlignment="Center" Content="{x:Static p:dnSpy_Resources.Bookmarks_Search}" ToolTip="{Binding SearchToolTip}" />
            <TextBox Grid.Column="2" Name="searchTextBox" Margin="5 0 0 0" Text="{Binding VM.FilterText, ValidatesOnDataErrors=True, ValidatesOnExceptions=True, UpdateSourceTrigger=PropertyChanged}" ToolTip="{Binding SearchToolTip}" />
            <StackPanel Grid.Column="3" Margin="2 3 0 3" Orientation="Horizontal">
                <Button Margin="0 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding SearchHelpCommand}" ToolTip="{Binding SearchHelpToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.StatusHelp}"/>
                </Button>
            </StackPanel>
        </Grid>
        <ListView
            Grid.Row="1"
            Visibility="{Binding VM.SomethingMatched, Converter={StaticResource BooleanToVisibilityConverter}}"
            Name="listView"
            MouseDoubleClick="ListView_MouseDoubleClick"
            VirtualizingStackPanel.IsVirtualizing="True"
            VirtualizingStackPanel.VirtualizationMode="Recycling"
            mvvm:InitDataTemplateAP.Initialize="True"
            mvvm:AutomationPeerMemoryLeakWorkaround.Initialize="True"
            ui:ListBoxSelectedItemsAP.SelectedItemsVM="{Binding VM.SelectedItems}"
            mvvm:GridViewColumnSorter.ColumnProvider="{Binding VM}"
            BorderThickness="0"
            img:DsImage.BackgroundBrush="{Binding Background, RelativeSource={RelativeSource Self}}"
            SelectionMode="Extended"
            ItemsSource="{Binding VM.AllItems}">
            <ListView.Resources>
                <Style x:Key="{x:Static GridView.GridViewScrollViewerStyleKey}" BasedOn="{StaticResource BookmarksTableGridViewScrollViewerStyle}" TargetType="{x:Type ScrollViewer}" />
                <local:BookmarkColumnConverter x:Key="bookmarkColumnConverter" />
            </ListView.Resources>
            <ListView.View>
                <GridView AllowsColumnReorder="True">
                    <GridViewColumn mvvm:GridViewColumnSorter.Id="{x:Static local:BookmarksWindowColumnIds.Name}" Header="{x:Static p:dnSpy_Resources.Column_Name}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <CheckBox Grid.Column="0" Focusable="False" IsChecked="{Binding IsEnabled}" />
                                    <!-- Using just a DsImage doesn't work without getting an image multiplied by DPI-factor (eg. 2 if 192 DPI),
                                         so use this weird way of showing an image. -->
                                    <ContentPresenter Grid.Column="1" Margin="5 0 0 0" Content="{Binding ImageReference, Mode=OneWay}">
                                        <ContentPresenter.ContentTemplate>
                                            <DataTemplate>
                                                <img:DsImage ImageReference="{Binding}"/>
                                            </DataTemplate>
                                        </ContentPresenter.ContentTemplate>
                                    </ContentPresenter>
                                    <ctrls:EditValueControl Grid.Column="2" Margin="5 0 0 0" ReadOnlyContent="{Binding NameObject, Mode=OneWay, Converter={StaticResource bookmarkColumnConverter}}" EditableValue="{Binding NameEditableValue}" EditValueProvider="{Binding NameEditValueProvider}" />
                                </Grid>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn mvvm:GridViewColumnSorter.Id="{x:Static local:BookmarksWindowColumnIds.Labels}" Header="{x:Static p:dnSpy_Resources.Column_Labels}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ctrls:EditValueControl ReadOnlyContent="{Binding LabelsObject, Mode=OneWay, Converter={StaticResource bookmarkColumnConverter}}" EditableValue="{Binding LabelsEditableValue}" EditValueProvider="{Binding LabelsEditValueProvider}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn mvvm:GridViewColumnSorter.Id="{x:Static local:BookmarksWindowColumnIds.Location}" Header="{x:Static p:dnSpy_Resources.Column_Location}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ContentPresenter Content="{Binding LocationObject, Mode=OneWay, Converter={StaticResource bookmarkColumnConverter}}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn mvvm:GridViewColumnSorter.Id="{x:Static local:BookmarksWindowColumnIds.Module}" Header="{x:Static p:dnSpy_Resources.Column_Module}">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ContentPresenter Content="{Binding ModuleObject, Mode=OneWay, Converter={StaticResource bookmarkColumnConverter}}" />
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>
        <Grid Grid.Row="1" Visibility="{Binding VM.NothingMatched, Converter={StaticResource BooleanToVisibilityConverter}}"
              mvvm:InitDataTemplateAP.Initialize="True"
              Background="{DynamicResource GridViewBackground}"
              img:DsImage.BackgroundBrush="{Binding Background, RelativeSource={RelativeSource Self}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <Label Content="{x:Static p:dnSpy_Resources.Bookmarks_NoMatch}"/>
                <Button Margin="5 0 0 0" Focusable="False" Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}" ToolTipService.ShowOnDisabled="True" Command="{Binding ResetSearchSettingsCommand}" ToolTip="{Binding ResetSearchSettingsToolTip}">
                    <img:DsImage ImageReference="{x:Static img:DsImages.ClearWindowContent}"/>
                </Button>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
