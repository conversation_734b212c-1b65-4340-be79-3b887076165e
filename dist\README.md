# DnSpyAI

DnSpyAI, dnSpy için geliştirilmiş bir yapay zeka entegrasyonu eklentisidir. B<PERSON> eklenti, dnSpy içinde kod analizi, düzenleme ve geliştirme süreçlerinde yapay zeka desteği sağlar.

![DnSpyAI Logo](images/dnspyai-logo.png)

## Özellikler

- **Kod Analizi**: Seçili kodu analiz eder, iyileştirme önerileri sunar
- **Kod Düzenleme**: Verilen talimatlara göre kodu otomatik olarak düzenler
- **AI Sohbet**: Yapay zeka ile doğrudan sohbet ederek kodlama sorularını yanıtlar
- **Kod Açıklama**: Karmaşık kod parçalarını açıklar ve dokümante eder

## Kurulum

Detaylı kurulum talimatları için [KURULUM.md](KURULUM.md) dos<PERSON><PERSON>na bakın.

Hızlı kurulum:

1. <PERSON><PERSON><PERSON> derley<PERSON>: `dotnet build`
2. <PERSON><PERSON><PERSON><PERSON> `bin/Debug/netstandard2.0/DnSpyAI.dll` dosyasını dnSpy'ın Extensions klasörüne kopyalayın
3. dnSpy'ı yeniden başlatın

## Kullanım

Eklenti kurulduktan sonra, dnSpy menüsünde "AI Asistan" seçeneği görünecektir. Bu menüden aşağıdaki işlemleri yapabilirsiniz:

- **AI Paneli Aç**: Yapay zeka ile sohbet etmek için bir panel açar
- **Kodu Analiz Et**: Seçili kodu analiz eder ve öneriler sunar
- **Kodu Düzenle**: Seçili kodu verilen talimatlara göre düzenler

## Ayarlar

Eklenti ayarlarına dnSpy menüsünden erişebilirsiniz. Burada aşağıdaki ayarları yapabilirsiniz:

- **API Anahtarı**: OpenAI API anahtarınızı girebilirsiniz
- **Model**: Kullanılacak AI modelini seçebilirsiniz (gpt-3.5-turbo, gpt-4 vb.)
- **Maksimum Token**: Yanıt uzunluğunu sınırlayabilirsiniz
- **Sistem Promptu**: Özel bir sistem promptu tanımlayabilirsiniz

## Geliştirme

Bu proje .NET Standard 2.0 ile geliştirilmiştir ve dnSpy'ın MEF (Managed Extensibility Framework) sistemini kullanmaktadır.

### Proje Yapısı

- **DnSpyAIPlugin.cs**: Ana eklenti sınıfı, MEF tarafından keşfedilir
- **AIPanel.cs**: AI ile etkileşim için kullanıcı arayüzü
- **AIService.cs**: OpenAI API ile iletişim kuran servis sınıfı
- **DnSpyAICommands.cs**: dnSpy menüsüne komutlar ekleyen sınıf
- **DnSpyAISettings.cs**: Eklenti ayarlarını yöneten sınıf

### Gereksinimler

- .NET Standard 2.0 uyumlu bir geliştirme ortamı
- Visual Studio 2019/2022 veya JetBrains Rider

### Derleme

```
dotnet build
```

## Gelecek Özellikler

- Kod tamamlama önerileri
- Otomatik hata ayıklama yardımcısı
- Çoklu dil desteği
- Offline mod desteği

## Lisans

MIT

## İletişim

Sorularınız ve önerileriniz için GitHub üzerinden iletişime geçebilirsiniz.
